@echo off
echo ========================================
echo    IP ROTATION VPN MANAGER
echo ========================================
echo.
echo Starting the VPN rotation application...
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo ❌ Dependencies not installed!
    echo.
    echo Please run install_requirements.bat first to install dependencies.
    echo.
    pause
    exit /b 1
)

REM Check if config.json exists
if not exist "config.json" (
    echo ❌ config.json not found!
    echo.
    echo Please make sure config.json exists with your VPN credentials.
    echo.
    pause
    exit /b 1
)

echo ✅ Dependencies found
echo ✅ Configuration found
echo.

REM Check for prepared servers
set server_count=0
for %%f in (ready_to_import\*.ovpn) do (
    set /a server_count+=1
)

if %server_count% gtr 0 (
    echo ✅ Found %server_count% prepared VPN servers
) else (
    echo ⚠️ No prepared servers found in ready_to_import folder
    echo   Run prepare_servers.bat to prepare your servers first
)

echo.
echo 🚀 Starting Electron application...
echo.

REM Start the application
npm start

REM If npm start fails, show error message
if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ Failed to start the application!
    echo.
    echo Possible solutions:
    echo 1. Run install_requirements.bat to install dependencies
    echo 2. Make sure Node.js is installed
    echo 3. Check that all files are present
    echo.
    pause
)

echo.
echo Application closed.
pause
