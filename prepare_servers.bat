@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    AUTO SERVER PREPARATION TOOL
echo ========================================
echo.
echo This script will:
echo - Find all .ovpn files in openvpn_configs folder
echo - Embed your credentials automatically  
echo - Prepare them for ready_to_import folder
echo.

REM Check for openvpn_configs folder
if not exist "openvpn_configs" (
    echo ❌ openvpn_configs folder not found!
    echo Please create it and add your .ovpn files.
    pause
    exit /b 1
)

REM Check for config.json
if not exist "config.json" (
    echo ❌ config.json not found!
    echo Please make sure config.json exists with your credentials.
    pause
    exit /b 1
)

echo ✅ Folders found!
echo.

REM Read credentials from config.json
echo 📋 Reading credentials from config.json...

REM Extract username and password from config.json
for /f "tokens=2 delims=:, " %%a in ('findstr "openvpn_username" config.json') do (
    set username=%%a
    set username=!username:"=!
)

for /f "tokens=2 delims=:, " %%a in ('findstr "openvpn_password" config.json') do (
    set password=%%a
    set password=!password:"=!
)

if "!username!"=="" (
    echo ❌ Could not find openvpn_username in config.json
    pause
    exit /b 1
)

if "!password!"=="" (
    echo ❌ Could not find openvpn_password in config.json
    pause
    exit /b 1
)

echo ✅ Credentials loaded for user: !username!
echo.

REM Create ready_to_import directory
if not exist "ready_to_import" mkdir "ready_to_import"

REM Count .ovpn files
set count=0
for %%f in (openvpn_configs\*.ovpn) do (
    set /a count+=1
)

if !count!==0 (
    echo ❌ No .ovpn files found in openvpn_configs folder
    pause
    exit /b 1
)

echo 🔍 Found !count! .ovpn files
echo.
echo 📊 Files found:
for %%f in (openvpn_configs\*.ovpn) do (
    echo    📄 %%~nxf
)

echo.
echo 🔄 Processing files...
echo.

set processed=0
set failed=0

REM Process each .ovpn file
for %%f in (openvpn_configs\*.ovpn) do (
    echo 📝 Processing: %%~nxf
    
    REM Read the original file and create new one with credentials
    (
        REM Copy original content, skipping existing auth lines
        for /f "usebackq delims=" %%l in ("%%f") do (
            set line=%%l
            if not "!line:auth-user-pass=!"=="!line!" (
                REM Skip auth-user-pass lines
            ) else if not "!line:<auth-user-pass>=!"=="!line!" (
                REM Skip auth block start
            ) else if not "!line:</auth-user-pass>=!"=="!line!" (
                REM Skip auth block end  
            ) else (
                echo !line!
            )
        )
        
        REM Add embedded credentials
        echo.
        echo # Auto-embedded credentials
        echo auth-user-pass
        echo ^<auth-user-pass^>
        echo !username!
        echo !password!
        echo ^</auth-user-pass^>
        
    ) > "ready_to_import\%%~nxf"
    
    if exist "ready_to_import\%%~nxf" (
        echo    ✅ %%~nxf
        set /a processed+=1
    ) else (
        echo    ❌ Failed to process %%~nxf
        set /a failed+=1
    )
)

echo.
echo ========================================
echo 🎉 Processing Complete!
echo ✅ Successfully prepared: !processed! files
if !failed! gtr 0 echo ❌ Failed: !failed! files
echo.
echo 📁 Prepared files are in: ready_to_import\
echo.

REM List prepared files
echo 📋 Prepared files:
for %%f in (ready_to_import\*.ovpn) do (
    echo    ✅ %%~nxf (with embedded credentials)
)

echo.
echo 🎯 Next Steps:
echo 1. Check the ready_to_import folder
echo 2. Run import_all.bat to import all servers to OpenVPN GUI
echo 3. Use the Electron app to connect to any server
echo 4. Start the auto-scheduler for automatic rotations
echo.

set /p choice="Would you like to run import_all.bat now? (y/n): "
if /i "!choice!"=="y" (
    echo.
    echo 🚀 Running import_all.bat...
    call import_all.bat
) else (
    echo.
    echo 🚀 Ready to use! Run import_all.bat when ready.
)

echo.
pause
