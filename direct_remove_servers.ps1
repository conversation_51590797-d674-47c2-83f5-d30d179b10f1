# Direct PowerShell script to remove OpenVPN GUI servers
# This targets the exact servers shown in your screenshot

Write-Host "========================================" -ForegroundColor Green
Write-Host "   DIRECT OPENVPN SERVER REMOVAL" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Step 1: Kill all OpenVPN processes
Write-Host "🛑 Stopping all OpenVPN processes..." -ForegroundColor Yellow
Get-Process -Name "openvpn-gui" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "openvpn" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 3
Write-Host "✅ All OpenVPN processes stopped" -ForegroundColor Green

# Step 2: Find and remove ALL .ovpn files from ALL locations
Write-Host ""
Write-Host "🗑️ Removing ALL .ovpn files from ALL locations..." -ForegroundColor Yellow

$locations = @(
    "C:\Program Files\OpenVPN\config",
    "C:\Program Files (x86)\OpenVPN\config",
    "$env:USERPROFILE\OpenVPN\config",
    "$env:APPDATA\OpenVPN\config",
    "$env:LOCALAPPDATA\OpenVPN\config",
    "$env:PROGRAMDATA\OpenVPN\config"
)

$totalRemoved = 0

foreach ($location in $locations) {
    if (Test-Path $location) {
        Write-Host "📁 Checking: $location" -ForegroundColor Cyan
        
        $files = Get-ChildItem -Path $location -Filter "*.ovpn" -ErrorAction SilentlyContinue
        
        if ($files) {
            foreach ($file in $files) {
                try {
                    Remove-Item $file.FullName -Force
                    Write-Host "   ✅ Removed: $($file.Name)" -ForegroundColor Green
                    $totalRemoved++
                } catch {
                    Write-Host "   ❌ Failed: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "   📄 No .ovpn files found" -ForegroundColor Gray
        }
    } else {
        Write-Host "   ❌ Directory not found: $location" -ForegroundColor Gray
    }
}

# Step 3: Remove specific server files by name (from your screenshot)
Write-Host ""
Write-Host "🎯 Targeting specific servers from screenshot..." -ForegroundColor Yellow

$serverNames = @(
    "af-04.protonvpn.udp",
    "af-05.protonvpn.udp", 
    "al-33.protonvpn.udp",
    "al-34.protonvpn.udp",
    "al-35.protonvpn.udp",
    "al-36.protonvpn.udp",
    "al-37.protonvpn.udp",
    "al-38.protonvpn.udp",
    "al-39.protonvpn.udp",
    "al-40.protonvpn.udp",
    "al-41.protonvpn.udp",
    "al-42.protonvpn.udp",
    "al-43.protonvpn.udp",
    "al-44.protonvpn.udp",
    "al-45.protonvpn.udp",
    "al-46.protonvpn.udp",
    "al-47.protonvpn.udp",
    "am-02.protonvpn.udp",
    "ao-05.protonvpn.udp"
)

foreach ($location in $locations) {
    if (Test-Path $location) {
        foreach ($serverName in $serverNames) {
            $filePath = Join-Path $location "$serverName.ovpn"
            if (Test-Path $filePath) {
                try {
                    Remove-Item $filePath -Force
                    Write-Host "   🎯 Targeted removal: $serverName.ovpn" -ForegroundColor Green
                    $totalRemoved++
                } catch {
                    Write-Host "   ❌ Failed to remove: $serverName.ovpn" -ForegroundColor Red
                }
            }
        }
    }
}

# Step 4: Nuclear registry cleanup
Write-Host ""
Write-Host "💥 Nuclear registry cleanup..." -ForegroundColor Yellow

$registryPaths = @(
    "HKCU:\Software\OpenVPN-GUI",
    "HKLM:\Software\OpenVPN-GUI",
    "HKCU:\Software\OpenVPN",
    "HKLM:\Software\OpenVPN"
)

foreach ($regPath in $registryPaths) {
    try {
        if (Test-Path $regPath) {
            Remove-Item -Path $regPath -Recurse -Force
            Write-Host "   💥 Nuked: $regPath" -ForegroundColor Green
        }
    } catch {
        Write-Host "   ⚠️ Could not nuke: $regPath" -ForegroundColor Yellow
    }
}

# Step 5: Remove OpenVPN GUI settings and cache
Write-Host ""
Write-Host "🧹 Removing OpenVPN GUI settings..." -ForegroundColor Yellow

$settingsPaths = @(
    "$env:APPDATA\OpenVPN-GUI",
    "$env:LOCALAPPDATA\OpenVPN-GUI",
    "$env:USERPROFILE\.openvpn-gui",
    "$env:TEMP\openvpn-gui*"
)

foreach ($settingsPath in $settingsPaths) {
    if (Test-Path $settingsPath) {
        try {
            Remove-Item -Path $settingsPath -Recurse -Force
            Write-Host "   🧹 Cleaned: $settingsPath" -ForegroundColor Green
        } catch {
            Write-Host "   ⚠️ Could not clean: $settingsPath" -ForegroundColor Yellow
        }
    }
}

# Step 6: Wait and restart OpenVPN GUI
Write-Host ""
Write-Host "🔄 Restarting OpenVPN GUI..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

$openvpnExe = "C:\Program Files\OpenVPN\bin\openvpn-gui.exe"
if (Test-Path $openvpnExe) {
    try {
        Start-Process -FilePath $openvpnExe
        Write-Host "✅ OpenVPN GUI restarted" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to restart OpenVPN GUI" -ForegroundColor Red
    }
} else {
    Write-Host "❌ OpenVPN GUI executable not found" -ForegroundColor Red
}

# Final summary
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "🎉 DIRECT REMOVAL COMPLETE!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Total files removed: $totalRemoved" -ForegroundColor White
Write-Host ""
Write-Host "💡 What was done:" -ForegroundColor Yellow
Write-Host "   • Killed all OpenVPN processes" -ForegroundColor White
Write-Host "   • Removed ALL .ovpn files from ALL locations" -ForegroundColor White
Write-Host "   • Targeted specific servers from your screenshot" -ForegroundColor White
Write-Host "   • Nuclear registry cleanup" -ForegroundColor White
Write-Host "   • Removed all OpenVPN GUI settings" -ForegroundColor White
Write-Host "   • Restarted OpenVPN GUI" -ForegroundColor White
Write-Host ""
Write-Host "🎯 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Right-click OpenVPN tray icon" -ForegroundColor White
Write-Host "   2. The server list should now be EMPTY" -ForegroundColor White
Write-Host "   3. If servers still appear, restart your computer" -ForegroundColor White
Write-Host ""

Write-Host "RESULT: Removed $totalRemoved servers successfully" -ForegroundColor Green
