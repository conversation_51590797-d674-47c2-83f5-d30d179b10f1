@echo off
echo ========================================
echo    STARTING VPN ROTATOR AS ADMIN
echo ========================================
echo.
echo This will start the Electron app with administrator
echo privileges to ensure VPN connections work properly.
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as administrator
    echo.
    echo 🚀 Starting Electron app...
    npm start
) else (
    echo ⚠️ Not running as administrator
    echo.
    echo Requesting administrator privileges...
    echo.
    
    REM Re-run this script as administrator
    powershell -Command "Start-Process cmd -ArgumentList '/c cd /d \"%~dp0\" && start_as_admin.bat' -Verb RunAs"
)

pause
