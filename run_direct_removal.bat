@echo off
echo ========================================
echo    DIRECT OPENVPN SERVER REMOVAL
echo ========================================
echo.
echo This will remove ALL servers from OpenVPN GUI.
echo Targeting the exact servers shown in your screenshot.
echo.

set /p confirm="Remove ALL servers from OpenVPN GUI? (y/n): "
if /i not "%confirm%"=="y" (
    echo Cancelled.
    pause
    exit /b 0
)

echo.
echo 🚀 Running direct removal PowerShell script...
echo.

REM Run the PowerShell script
powershell -ExecutionPolicy Bypass -File "%~dp0direct_remove_servers.ps1"

echo.
echo ========================================
echo.
echo 💡 Check the OpenVPN tray icon now!
echo    Right-click it to see if servers are gone.
echo.
pause
