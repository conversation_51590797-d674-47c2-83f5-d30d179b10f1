# Simple PowerShell script to handle OpenVPN GUI popups
Add-Type -AssemblyName System.Windows.Forms

function Click-OpenVPNPopup {
    try {
        Write-Host "Looking for OpenVPN popups..."
        
        # Find all OpenVPN GUI processes
        $openvpnProcesses = Get-Process -Name "openvpn-gui" -ErrorAction SilentlyContinue
        
        if ($openvpnProcesses) {
            Write-Host "Found $($openvpnProcesses.Count) OpenVPN GUI process(es)"
            
            foreach ($process in $openvpnProcesses) {
                if ($process.MainWindowTitle -ne "" -and $process.MainWindowHandle -ne 0) {
                    Write-Host "Found window: '$($process.MainWindowTitle)'"
                    
                    # Focus the window and send ENTER
                    $null = [System.Windows.Forms.SendKeys]::SendWait("%{TAB}")
                    Start-Sleep -Milliseconds 200
                    [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")
                    
                    Write-Host "Sent ENTER key to OpenVPN window"
                    return $true
                }
            }
        }
        
        # Alternative method: Look for any window with "OpenVPN" in the title
        $allProcesses = Get-Process | Where-Object { 
            $_.MainWindowTitle -like "*OpenVPN*" -and 
            $_.MainWindowTitle -ne "" 
        }
        
        foreach ($process in $allProcesses) {
            Write-Host "Found OpenVPN-related window: '$($process.MainWindowTitle)'"
            [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")
            Write-Host "Sent ENTER to window"
            return $true
        }
        
        Write-Host "No OpenVPN popups found"
        return $false
        
    } catch {
        Write-Host "Error: $($_.Exception.Message)"
        return $false
    }
}

# Run the popup handler
$result = Click-OpenVPNPopup
if ($result) {
    Write-Host "SUCCESS: Popup handled"
} else {
    Write-Host "INFO: No popups to handle"
}
