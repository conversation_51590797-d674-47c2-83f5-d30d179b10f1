<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proton VPN IP Rotator</title>
    <link rel="stylesheet" href="pixel_styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>[VPN-ROTATOR v1.0]</h1>
            <div class="status-bar">
                <div class="status-item">
                    <span class="label">Current IP:</span>
                    <span id="current-ip" class="value">Loading...</span>
                </div>
                <div class="status-item">
                    <span class="label">Available Servers:</span>
                    <span id="server-count" class="value">0</span>
                </div>
                <div class="status-item">
                    <span class="label">Action:</span>
                    <span class="value">Click to Connect</span>
                </div>
            </div>
        </header>

        <div class="tabs">
            <button class="tab-button active" onclick="showTab('servers')">VPN Servers</button>
            <button class="tab-button" onclick="showTab('scheduler')">Auto Scheduler</button>
            <button class="tab-button" onclick="showTab('tools')">Tools</button>
        </div>

        <!-- Servers Tab -->
        <div id="servers-tab" class="tab-content active">
            <div class="servers-panel">
                <h3>Available VPN Servers</h3>
                <p>Click any server to connect instantly! (Auto-disconnects from previous server)</p>

                <div class="server-filters">
                    <input type="text" id="server-search" placeholder="Search servers..." class="search-input">
                    <select id="country-filter" class="filter-select">
                        <option value="">All Countries</option>
                    </select>
                </div>

                <div id="servers-list" class="servers-list">
                    <div class="loading">Loading servers...</div>
                </div>
            </div>
        </div>

        <!-- Scheduler Tab -->
        <div id="scheduler-tab" class="tab-content">
            <div class="scheduler-panel">
                <h3>Auto IP Rotation Scheduler</h3>

                <div class="scheduler-section">
                    <h4>Current Status</h4>
                    <div class="status-display">
                        <div class="status-row">
                            <span>Scheduler Status:</span>
                            <span id="scheduler-status" class="value">Stopped</span>
                        </div>
                        <div class="status-row">
                            <span>Rotation Count:</span>
                            <span id="rotation-count" class="value">0</span>
                        </div>
                        <div class="status-row">
                            <span>Last Rotation:</span>
                            <span id="last-rotation" class="value">Never</span>
                        </div>
                        <div class="status-row">
                            <span>Next Rotation:</span>
                            <span id="next-rotation" class="value">Not scheduled</span>
                        </div>
                    </div>
                </div>

                <div class="scheduler-section">
                    <h4>Schedule Settings</h4>
                    <div class="setting-group">
                        <label for="rotation-interval">Rotation Interval (minutes):</label>
                        <input type="number" id="rotation-interval" min="1" max="1440" value="30">
                    </div>

                    <div class="quick-intervals">
                        <h5>Quick Presets:</h5>
                        <div class="button-group">
                            <button class="btn btn-small" onclick="setInterval(5)">[5M]</button>
                            <button class="btn btn-small" onclick="setInterval(15)">[15M]</button>
                            <button class="btn btn-small" onclick="setInterval(30)">[30M]</button>
                            <button class="btn btn-small" onclick="setInterval(60)">[1H]</button>
                            <button class="btn btn-small" onclick="setInterval(120)">[2H]</button>
                        </div>
                    </div>

                    <div class="button-group">
                        <button id="start-scheduler-btn" class="btn btn-success">[START SCHEDULER]</button>
                        <button id="stop-scheduler-btn" class="btn btn-danger" disabled>[STOP SCHEDULER]</button>
                    </div>
                </div>

                <div class="scheduler-section">
                    <h4>File Cleanup</h4>
                    <div class="cleanup-info">
                        <div class="status-row">
                            <span>Unnecessary Files:</span>
                            <span id="cleanup-file-count" class="value">Checking...</span>
                        </div>
                        <div class="status-row">
                            <span>Total Size:</span>
                            <span id="cleanup-file-size" class="value">Checking...</span>
                        </div>
                    </div>

                    <div class="button-group">
                        <button id="cleanup-now-btn" class="btn btn-secondary">[CLEANUP NOW]</button>
                        <button id="refresh-cleanup-btn" class="btn btn-secondary">[REFRESH]</button>
                    </div>

                    <div class="info-text">
                        <p>• Auto-cleanup runs after each rotation</p>
                        <p>• Removes temp files, old logs, auth files</p>
                        <p>• Keeps only essential working files</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tools Tab -->
        <div id="tools-tab" class="tab-content">
            <div class="tools-panel">
                <h3>VPN Tools</h3>

                <div class="tool-section">
                    <h4>Connection Tools</h4>
                    <div class="button-group">
                        <button id="disconnect-btn" class="btn btn-danger">[DISCONNECT VPN]</button>
                        <button id="refresh-ip-btn" class="btn btn-secondary">[REFRESH IP]</button>
                    </div>
                </div>

                <div class="tool-section">
                    <h4>Server Management</h4>
                    <div class="button-group">
                        <button id="remove-all-servers-btn" class="btn btn-danger">[REMOVE ALL SERVERS]</button>
                        <button id="remove-servers-batch-btn" class="btn btn-danger">[BATCH REMOVE]</button>
                    </div>
                    <div class="button-group">
                        <button id="clear-openvpn-configs-btn" class="btn btn-danger">[CLEAR OPENVPN CONFIGS]</button>
                    </div>
                    <div class="info-text">
                        <p>⚠️ <strong>Warning:</strong> These actions cannot be undone!</p>
                        <p>• REMOVE ALL removes servers from OpenVPN GUI app</p>
                        <p>• BATCH REMOVE uses comprehensive batch file method</p>
                        <p>• CLEAR CONFIGS deletes all files from OpenVPN config directory</p>
                    </div>
                </div>

                <div class="tool-section">
                    <h4>Quick Actions</h4>
                    <div class="button-group">
                        <button id="random-server-btn" class="btn btn-primary">[RANDOM SERVER]</button>
                        <button id="refresh-servers-btn" class="btn btn-secondary">[REFRESH SERVERS]</button>
                    </div>
                    <div class="button-group">
                        <button id="open-folder-btn" class="btn btn-secondary">[OPEN OPENVPN GUI]</button>
                    </div>
                </div>

                <div class="tool-section">
                    <h4>Information</h4>
                    <div class="info-text">
                        <p>• Click any server to connect instantly</p>
                        <p>• Auto-disconnects from previous server</p>
                        <p>• Use [DISCONNECT VPN] to stop connection</p>
                        <p>• Your IP will change when connected</p>
                        <p>• [REFRESH SERVERS] scans for new .ovpn files</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="new_renderer.js"></script>
</body>
</html>
