# OpenVPN Configs Folder

This folder is where you add your raw .ovpn server files.

## Simple Structure

Just put your .ovpn files directly in this folder:

```
openvpn_configs/
├── server1.ovpn
├── server2.ovpn
├── server3.ovpn
├── server4.ovpn
├── us-server.ovpn
├── uk-server.ovpn
└── any-other-server.ovpn
```

## How It Works

1. **Add your .ovpn files** to this folder (with or without subfolders)
2. **Run `prepare_all_servers.bat`** to automatically:
   - Scan all .ovpn files recursively
   - Embed your credentials from config.json
   - Prepare them for import in ready_to_import folder
3. **Run `import_all.bat`** to import all prepared servers to OpenVPN GUI
4. **Use the Electron app** to connect and rotate between servers

## File Processing

The preparation script will:
- Keep the original filename: `server1.ovpn` → `server1.ovpn`
- Embed your credentials automatically
- Create ready-to-import files in the `ready_to_import` folder

All files keep their original names for simplicity.
