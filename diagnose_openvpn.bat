@echo off
echo ========================================
echo    OPENVPN GUI DIAGNOSTIC TOOL
echo ========================================
echo.
echo This will show you exactly where OpenVPN servers are stored.
echo.

echo 🔍 Checking OpenVPN GUI processes...
tasklist /FI "IMAGENAME eq openvpn-gui.exe" 2>nul | find "openvpn-gui.exe" >nul
if %errorlevel%==0 (
    echo ✅ OpenVPN GUI is running
) else (
    echo ❌ OpenVPN GUI is not running
)

echo.
echo 🔍 Checking possible config directories...
echo.

set found_configs=0

echo [1] Checking: C:\Program Files\OpenVPN\config\
if exist "C:\Program Files\OpenVPN\config\" (
    echo ✅ Directory exists
    dir "C:\Program Files\OpenVPN\config\*.ovpn" /b 2>nul | find ".ovpn" >nul
    if %errorlevel%==0 (
        echo 📄 .ovpn files found:
        for %%f in ("C:\Program Files\OpenVPN\config\*.ovpn") do (
            echo    - %%~nxf
            set /a found_configs+=1
        )
    ) else (
        echo 📄 No .ovpn files found
    )
) else (
    echo ❌ Directory does not exist
)

echo.
echo [2] Checking: %USERPROFILE%\OpenVPN\config\
if exist "%USERPROFILE%\OpenVPN\config\" (
    echo ✅ Directory exists
    dir "%USERPROFILE%\OpenVPN\config\*.ovpn" /b 2>nul | find ".ovpn" >nul
    if %errorlevel%==0 (
        echo 📄 .ovpn files found:
        for %%f in ("%USERPROFILE%\OpenVPN\config\*.ovpn") do (
            echo    - %%~nxf
            set /a found_configs+=1
        )
    ) else (
        echo 📄 No .ovpn files found
    )
) else (
    echo ❌ Directory does not exist
)

echo.
echo [3] Checking: %APPDATA%\OpenVPN\config\
if exist "%APPDATA%\OpenVPN\config\" (
    echo ✅ Directory exists
    dir "%APPDATA%\OpenVPN\config\*.ovpn" /b 2>nul | find ".ovpn" >nul
    if %errorlevel%==0 (
        echo 📄 .ovpn files found:
        for %%f in ("%APPDATA%\OpenVPN\config\*.ovpn") do (
            echo    - %%~nxf
            set /a found_configs+=1
        )
    ) else (
        echo 📄 No .ovpn files found
    )
) else (
    echo ❌ Directory does not exist
)

echo.
echo [4] Checking: %PROGRAMDATA%\OpenVPN\config\
if exist "%PROGRAMDATA%\OpenVPN\config\" (
    echo ✅ Directory exists
    dir "%PROGRAMDATA%\OpenVPN\config\*.ovpn" /b 2>nul | find ".ovpn" >nul
    if %errorlevel%==0 (
        echo 📄 .ovpn files found:
        for %%f in ("%PROGRAMDATA%\OpenVPN\config\*.ovpn") do (
            echo    - %%~nxf
            set /a found_configs+=1
        )
    ) else (
        echo 📄 No .ovpn files found
    )
) else (
    echo ❌ Directory does not exist
)

echo.
echo 🔍 Checking registry entries...
echo.

echo [Registry] HKEY_CURRENT_USER\Software\OpenVPN-GUI
reg query "HKEY_CURRENT_USER\Software\OpenVPN-GUI" >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Registry key exists
    reg query "HKEY_CURRENT_USER\Software\OpenVPN-GUI\configs" >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ Configs subkey exists
    ) else (
        echo ❌ Configs subkey does not exist
    )
) else (
    echo ❌ Registry key does not exist
)

echo.
echo ========================================
echo 📊 DIAGNOSTIC SUMMARY
echo ========================================
echo.
echo Total .ovpn files found: %found_configs%
echo.

if %found_configs% gtr 0 (
    echo 💡 SOLUTION: Files found in config directories
    echo    Use the working removal method below.
) else (
    echo 💡 MYSTERY: No .ovpn files found in standard locations
    echo    Servers might be stored differently or in registry only.
)

echo.
echo 🎯 Next steps:
echo 1. Note which directories have files
echo 2. Try the simple removal method below
echo 3. Check OpenVPN GUI tray icon after removal
echo.
pause
