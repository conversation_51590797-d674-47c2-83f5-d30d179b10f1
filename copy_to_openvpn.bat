@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    COPY SERVERS TO OPENVPN CONFIG DIR
echo ========================================
echo.
echo This script will:
echo - Copy all prepared .ovpn files to OpenVPN config directory
echo - Make them immediately available in OpenVPN GUI
echo - Ensure they appear in the connection list
echo.

REM Check for ready_to_import folder
if not exist "ready_to_import" (
    echo ❌ ready_to_import folder not found!
    echo Please run prepare_servers.bat first to prepare your servers.
    pause
    exit /b 1
)

REM Define OpenVPN config directory
set "openvpn_config_dir=C:\Program Files\OpenVPN\config"

REM Check if OpenVPN config directory exists
if not exist "%openvpn_config_dir%" (
    echo ❌ OpenVPN config directory not found at: %openvpn_config_dir%
    echo.
    echo Trying alternative location...
    set "openvpn_config_dir=%USERPROFILE%\OpenVPN\config"
    
    if not exist "!openvpn_config_dir!" (
        echo ❌ OpenVPN config directory not found at: !openvpn_config_dir!
        echo.
        echo Please install OpenVPN GUI or create the config directory manually.
        pause
        exit /b 1
    )
)

echo ✅ OpenVPN config directory found: %openvpn_config_dir%
echo.

REM Count .ovpn files in ready_to_import
set count=0
for %%f in (ready_to_import\*.ovpn) do (
    set /a count+=1
)

if !count!==0 (
    echo ❌ No .ovpn files found in ready_to_import folder
    echo Please run prepare_servers.bat first to prepare your servers.
    pause
    exit /b 1
)

echo 🔍 Found !count! prepared servers
echo.
echo 📊 Servers to copy:
for %%f in (ready_to_import\*.ovpn) do (
    echo    📄 %%~nxf
)

echo.
echo 🚀 Starting copy process...
echo.

set copied=0
set failed=0

REM Copy each .ovpn file to OpenVPN config directory
for %%f in (ready_to_import\*.ovpn) do (
    echo 📝 Copying: %%~nxf
    
    copy "%%f" "%openvpn_config_dir%\" >nul 2>&1
    
    if exist "%openvpn_config_dir%\%%~nxf" (
        echo    ✅ %%~nxf copied successfully
        set /a copied+=1
    ) else (
        echo    ❌ Failed to copy %%~nxf
        set /a failed+=1
    )
)

echo.
echo ========================================
echo 🎉 Copy Process Complete!
echo ✅ Successfully copied: !copied! servers
if !failed! gtr 0 echo ❌ Failed to copy: !failed! servers
echo.

echo 📁 Servers are now in: %openvpn_config_dir%
echo.

echo 📋 Available servers:
for %%f in (ready_to_import\*.ovpn) do (
    if exist "%openvpn_config_dir%\%%~nxf" (
        echo    ✅ %%~nxf
    )
)

echo.
echo 🎯 Next Steps:
echo 1. Restart OpenVPN GUI (if running)
echo 2. Right-click OpenVPN GUI tray icon to see your servers
echo 3. Use the Electron app to connect to any server
echo 4. Start the auto-scheduler for automatic rotations
echo.

echo 💡 Tips:
echo - Servers should now appear in OpenVPN GUI immediately
echo - You can connect by right-clicking the OpenVPN tray icon
echo - The Electron app will automatically detect all servers
echo.

REM Ask if user wants to restart OpenVPN GUI
set /p restart="Would you like to restart OpenVPN GUI now? (y/n): "
if /i "!restart!"=="y" (
    echo.
    echo 🔄 Restarting OpenVPN GUI...
    
    REM Kill existing OpenVPN GUI processes
    taskkill /f /im openvpn-gui.exe >nul 2>&1
    timeout /t 2 /nobreak >nul
    
    REM Start OpenVPN GUI
    start "" "C:\Program Files\OpenVPN\bin\openvpn-gui.exe"
    echo ✅ OpenVPN GUI restarted!
)

echo.
set /p choice="Would you like to start the Electron app now? (y/n): "
if /i "!choice!"=="y" (
    echo.
    echo 🚀 Starting Electron app...
    start "" npm start
    echo ✅ Electron app started! Check the new window.
) else (
    echo.
    echo 🚀 Ready to use! Start the Electron app with: npm start
)

echo.
echo 🌍 Your VPN servers are now ready for use!
echo.
pause
