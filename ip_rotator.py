#!/usr/bin/env python3
"""
Proton VPN IP Rotation Script
Automatically rotates IP addresses by switching between Proton VPN servers
"""

import os
import sys
import time
import json
import random
import logging
import subprocess
import threading
from pathlib import Path
from datetime import datetime, timedelta
import requests
from typing import List, Dict, Optional

# Try to import Windows-specific manager and server manager
try:
    from windows_vpn_manager import WindowsVPNManager
    WINDOWS_MANAGER_AVAILABLE = True
except ImportError:
    WINDOWS_MANAGER_AVAILABLE = False

try:
    from server_manager import ServerManager
    SERVER_MANAGER_AVAILABLE = True
except ImportError:
    SERVER_MANAGER_AVAILABLE = False

try:
    from health_monitor import HealthMonitor
    HEALTH_MONITOR_AVAILABLE = True
except ImportError:
    HEALTH_MONITOR_AVAILABLE = False

class ProtonVPNRotator:
    def __init__(self, config_file: str = "config.json"):
        """Initialize the VPN rotator with configuration"""
        self.config_file = config_file
        self.config = self.load_config()
        self.setup_logging()
        self.current_process = None
        self.current_server = None
        self.rotation_thread = None
        self.running = False

        # Initialize Windows-specific manager if available
        if WINDOWS_MANAGER_AVAILABLE and sys.platform == "win32":
            self.windows_manager = WindowsVPNManager(self.logger)
        else:
            self.windows_manager = None

        # Initialize server manager if available
        if SERVER_MANAGER_AVAILABLE:
            self.server_manager = ServerManager(self.config, self.logger)
        else:
            self.server_manager = None

        # Initialize health monitor if available
        if HEALTH_MONITOR_AVAILABLE:
            self.health_monitor = HealthMonitor(self.config, self.logger)
            self._setup_health_callbacks()
        else:
            self.health_monitor = None
        
    def load_config(self) -> Dict:
        """Load configuration from file or create default"""
        default_config = {
            "openvpn_configs_dir": "./openvpn_configs",
            "openvpn_username": "",
            "openvpn_password": "",
            "rotation_interval_minutes": 30,
            "preferred_countries": [],  # Empty means use all available
            "exclude_countries": [],
            "max_retries": 3,
            "retry_delay_seconds": 10,
            "check_ip_url": "https://api.ipify.org",
            "openvpn_executable": "openvpn",
            "log_level": "INFO",
            "rotation_strategy": "avoid_recent"
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                # Merge with defaults to ensure all keys exist
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            except Exception as e:
                print(f"Error loading config: {e}. Using defaults.")
                
        # Create default config file
        with open(self.config_file, 'w') as f:
            json.dump(default_config, f, indent=4)
        print(f"Created default config file: {self.config_file}")
        print("Please edit the config file with your Proton VPN credentials and settings.")
        return default_config
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_level = getattr(logging, self.config.get('log_level', 'INFO').upper())
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('vpn_rotator.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def _setup_health_callbacks(self):
        """Setup health monitor callbacks"""
        if not self.health_monitor:
            return

        # Callback for connection loss - trigger rotation
        def on_connection_lost(data):
            self.logger.warning(f"Connection lost detected: {data}")
            if self.running:
                self.logger.info("Attempting to rotate IP due to connection loss")
                threading.Thread(target=self.rotate_ip, daemon=True).start()

        # Callback for IP changes - log them
        def on_ip_changed(data):
            self.logger.info(f"IP changed from {data.get('old_ip')} to {data.get('new_ip')}")

        # Callback for health errors - log them
        def on_health_error(data):
            self.logger.error(f"Health check error: {data.get('message')}")

        self.health_monitor.add_callback("connection_lost", on_connection_lost)
        self.health_monitor.add_callback("ip_changed", on_ip_changed)
        self.health_monitor.add_callback("health_error", on_health_error)
    
    def get_current_ip(self) -> Optional[str]:
        """Get current public IP address"""
        try:
            response = requests.get(self.config['check_ip_url'], timeout=10)
            if response.status_code == 200:
                return response.text.strip()
        except Exception as e:
            self.logger.error(f"Failed to get current IP: {e}")
        return None
    
    def get_available_configs(self) -> List[str]:
        """Get list of available OpenVPN configuration files"""
        configs_dir = Path(self.config['openvpn_configs_dir'])
        if not configs_dir.exists():
            self.logger.error(f"OpenVPN configs directory not found: {configs_dir}")
            return []
        
        config_files = list(configs_dir.glob("*.ovpn"))
        
        # Filter by preferred countries if specified
        if self.config['preferred_countries']:
            filtered_configs = []
            for config_file in config_files:
                for country in self.config['preferred_countries']:
                    if country.lower() in config_file.name.lower():
                        filtered_configs.append(config_file)
                        break
            config_files = filtered_configs
        
        # Exclude countries if specified
        if self.config['exclude_countries']:
            filtered_configs = []
            for config_file in config_files:
                exclude = False
                for country in self.config['exclude_countries']:
                    if country.lower() in config_file.name.lower():
                        exclude = True
                        break
                if not exclude:
                    filtered_configs.append(config_file)
            config_files = filtered_configs
        
        return [str(f) for f in config_files]
    
    def disconnect_vpn(self):
        """Disconnect current VPN connection"""
        if self.current_process:
            try:
                self.logger.info("Disconnecting from VPN...")
                self.current_process.terminate()
                self.current_process.wait(timeout=10)
                self.current_process = None
                self.current_server = None
                time.sleep(2)  # Wait for disconnection
            except Exception as e:
                self.logger.error(f"Error disconnecting VPN: {e}")
                try:
                    self.current_process.kill()
                except:
                    pass
                self.current_process = None

        # Use Windows manager for cleanup if available
        if self.windows_manager:
            self.windows_manager.cleanup()
    
    def connect_to_server(self, config_path: str) -> bool:
        """Connect to a specific VPN server"""
        auth_file = "auth.txt"
        try:
            self.logger.info(f"Connecting to server: {os.path.basename(config_path)}")

            # Create auth file
            with open(auth_file, 'w') as f:
                f.write(f"{self.config['openvpn_username']}\n")
                f.write(f"{self.config['openvpn_password']}\n")

            # Use Windows manager if available
            if self.windows_manager:
                self.current_process = self.windows_manager.create_openvpn_service(
                    config_path, auth_file
                )
                # Wait for connection with Windows-specific checks
                if self.windows_manager.wait_for_connection():
                    # Flush DNS for clean connection
                    self.windows_manager.flush_dns()
                    connection_success = True
                else:
                    connection_success = False
            else:
                # Standard OpenVPN process
                cmd = [
                    self.config['openvpn_executable'],
                    "--config", config_path,
                    "--auth-user-pass", auth_file,
                    "--daemon"
                ]

                self.current_process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )

                # Wait a bit for connection to establish
                time.sleep(10)
                connection_success = True

            if connection_success:
                # Verify IP change
                time.sleep(3)  # Additional wait for IP to propagate
                new_ip = self.get_current_ip()
                if new_ip:
                    self.current_server = os.path.basename(config_path)
                    self.logger.info(f"Successfully connected! New IP: {new_ip}")
                    return True
                else:
                    self.logger.error("Failed to verify IP change")
                    return False
            else:
                self.logger.error("Failed to establish VPN connection")
                return False

        except Exception as e:
            self.logger.error(f"Error connecting to server: {e}")
            return False
        finally:
            # Clean up auth file
            if os.path.exists(auth_file):
                os.remove(auth_file)
    
    def rotate_ip(self):
        """Rotate to a new IP address"""
        # Disconnect current connection
        self.disconnect_vpn()

        # Use server manager if available, otherwise fall back to old method
        if self.server_manager:
            return self._rotate_with_server_manager()
        else:
            return self._rotate_legacy()

    def _rotate_with_server_manager(self):
        """Rotate using the advanced server manager"""
        strategy = self.config.get('rotation_strategy', 'avoid_recent')

        for attempt in range(self.config['max_retries']):
            server = self.server_manager.get_next_server(strategy)
            if not server:
                self.logger.error("No servers available from server manager")
                return False

            # Don't reconnect to the same server
            if (self.current_server and
                self.current_server == os.path.basename(server.config_path)):
                continue

            if self.connect_to_server(server.config_path):
                self.server_manager.record_server_usage(server)
                return True

            self.logger.warning(f"Connection attempt {attempt + 1} failed, retrying...")
            time.sleep(self.config['retry_delay_seconds'])

        self.logger.error("Failed to connect after all retry attempts")
        return False

    def _rotate_legacy(self):
        """Legacy rotation method using simple config file selection"""
        configs = self.get_available_configs()
        if not configs:
            self.logger.error("No OpenVPN configuration files found!")
            return False

        # Try to connect to a random server
        for attempt in range(self.config['max_retries']):
            config_path = random.choice(configs)

            # Don't reconnect to the same server
            if self.current_server and self.current_server == os.path.basename(config_path):
                continue

            if self.connect_to_server(config_path):
                return True

            self.logger.warning(f"Connection attempt {attempt + 1} failed, retrying...")
            time.sleep(self.config['retry_delay_seconds'])

        self.logger.error("Failed to connect after all retry attempts")
        return False
    
    def rotation_loop(self):
        """Main rotation loop running in separate thread"""
        while self.running:
            try:
                interval_seconds = self.config['rotation_interval_minutes'] * 60
                self.logger.info(f"Next rotation in {self.config['rotation_interval_minutes']} minutes")
                
                # Wait for the interval or until stopped
                for _ in range(interval_seconds):
                    if not self.running:
                        break
                    time.sleep(1)
                
                if self.running:
                    self.rotate_ip()
                    
            except Exception as e:
                self.logger.error(f"Error in rotation loop: {e}")
                time.sleep(60)  # Wait before retrying
    
    def start(self):
        """Start the IP rotation service"""
        if not self.config['openvpn_username'] or not self.config['openvpn_password']:
            self.logger.error("OpenVPN username and password must be configured!")
            return False
        
        self.logger.info("Starting Proton VPN IP Rotator...")
        
        # Initial connection
        if not self.rotate_ip():
            self.logger.error("Failed to establish initial VPN connection")
            return False
        
        # Start rotation loop
        self.running = True
        self.rotation_thread = threading.Thread(target=self.rotation_loop, daemon=True)
        self.rotation_thread.start()

        # Start health monitoring if available
        if self.health_monitor:
            monitor_interval = self.config.get('health_check_interval_seconds', 60)
            self.health_monitor.start_monitoring(monitor_interval)

        self.logger.info("IP Rotator started successfully!")
        return True
    
    def stop(self):
        """Stop the IP rotation service"""
        self.logger.info("Stopping IP Rotator...")
        self.running = False
        
        if self.rotation_thread:
            self.rotation_thread.join(timeout=5)
        
        self.disconnect_vpn()
        self.logger.info("IP Rotator stopped")
    
    def status(self):
        """Get current status"""
        current_ip = self.get_current_ip()
        status = {
            "running": self.running,
            "current_server": self.current_server,
            "current_ip": current_ip,
            "available_configs": len(self.get_available_configs())
        }

        # Add server manager stats if available
        if self.server_manager:
            status["server_stats"] = self.server_manager.get_server_stats()

        # Add health monitor stats if available
        if self.health_monitor:
            status["health"] = self.health_monitor.get_health_summary()

        return status

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Proton VPN IP Rotator")
    parser.add_argument("--config", default="config.json", help="Configuration file path")
    parser.add_argument("--rotate-now", action="store_true", help="Rotate IP immediately and exit")
    parser.add_argument("--status", action="store_true", help="Show current status")
    parser.add_argument("--stop", action="store_true", help="Stop running rotator")
    
    args = parser.parse_args()
    
    rotator = ProtonVPNRotator(args.config)
    
    if args.status:
        status = rotator.status()
        print(json.dumps(status, indent=2))
        return
    
    if args.rotate_now:
        success = rotator.rotate_ip()
        sys.exit(0 if success else 1)
    
    if args.stop:
        rotator.stop()
        return
    
    # Start the rotator
    try:
        if rotator.start():
            print("IP Rotator is running. Press Ctrl+C to stop.")
            while True:
                time.sleep(1)
    except KeyboardInterrupt:
        rotator.stop()
        print("\nIP Rotator stopped by user")

if __name__ == "__main__":
    main()
