@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    IMPORT ALL SERVERS TO OPENVPN GUI
echo ========================================
echo.
echo This script will copy all prepared servers from ready_to_import
echo to the OpenVPN config directory for immediate use.
echo.

REM Check for ready_to_import folder
if not exist "ready_to_import" (
    echo ❌ ready_to_import folder not found!
    echo Please run prepare_servers.bat first to prepare your servers.
    pause
    exit /b 1
)

REM Define OpenVPN config directory
set "openvpn_config_dir=C:\Program Files\OpenVPN\config"

REM Check if OpenVPN config directory exists
if not exist "%openvpn_config_dir%" (
    echo ❌ OpenVPN config directory not found at: %openvpn_config_dir%
    echo.
    echo Trying alternative location...
    set "openvpn_config_dir=%USERPROFILE%\OpenVPN\config"
    
    if not exist "!openvpn_config_dir!" (
        echo ❌ OpenVPN config directory not found at: !openvpn_config_dir!
        echo.
        echo Please install OpenVPN GUI or create the config directory manually.
        pause
        exit /b 1
    )
)

echo ✅ OpenVPN config directory found: %openvpn_config_dir%
echo.

REM Count .ovpn files in ready_to_import
set count=0
for %%f in (ready_to_import\*.ovpn) do (
    set /a count+=1
)

if !count!==0 (
    echo ❌ No .ovpn files found in ready_to_import folder
    echo Please run prepare_servers.bat first to prepare your servers.
    pause
    exit /b 1
)

echo 🔍 Found !count! prepared servers
echo.
echo 🚀 Copying servers to OpenVPN config directory...
echo.

set copied=0
set failed=0

REM Copy each .ovpn file to OpenVPN config directory
for %%f in (ready_to_import\*.ovpn) do (
    echo 📝 Copying: %%~nxf
    
    copy "%%f" "%openvpn_config_dir%\" >nul 2>&1
    
    if exist "%openvpn_config_dir%\%%~nxf" (
        echo    ✅ %%~nxf
        set /a copied+=1
    ) else (
        echo    ❌ Failed to copy %%~nxf
        set /a failed+=1
    )
)

echo.
echo ========================================
echo 🎉 Import Complete!
echo ✅ Successfully imported: !copied! servers
if !failed! gtr 0 echo ❌ Failed to import: !failed! servers
echo.
echo 📁 Servers are now in: %openvpn_config_dir%
echo.

echo 🎯 Next Steps:
echo 1. Restart OpenVPN GUI (if running)
echo 2. Right-click OpenVPN GUI tray icon to see your servers
echo 3. Use the Electron app to connect to any server
echo 4. Start the auto-scheduler for automatic rotations
echo.

REM Ask if user wants to restart OpenVPN GUI
set /p restart="Would you like to restart OpenVPN GUI now? (y/n): "
if /i "!restart!"=="y" (
    echo.
    echo 🔄 Restarting OpenVPN GUI...
    
    REM Kill existing OpenVPN GUI processes
    taskkill /f /im openvpn-gui.exe >nul 2>&1
    timeout /t 2 /nobreak >nul
    
    REM Start OpenVPN GUI
    start "" "C:\Program Files\OpenVPN\bin\openvpn-gui.exe"
    echo ✅ OpenVPN GUI restarted!
)

echo.
echo 🌍 Your !copied! VPN servers are now ready for use!
echo.
pause
