# PowerShell script to forcefully remove all OpenVPN GUI servers
# Run as Administrator for best results

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   FORCE REMOVE ALL OPENVPN SERVERS" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Force kill all OpenVPN processes
Write-Host "🛑 Force stopping all OpenVPN processes..." -ForegroundColor Yellow
try {
    Get-Process -Name "openvpn-gui" -ErrorAction SilentlyContinue | Stop-Process -Force
    Get-Process -Name "openvpn" -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "✅ OpenVPN processes stopped" -ForegroundColor Green
} catch {
    Write-Host "⚠️ No OpenVPN processes were running" -ForegroundColor Yellow
}

Start-Sleep -Seconds 3

# Step 2: Find and remove config files from ALL possible locations
Write-Host ""
Write-Host "🗑️ Removing config files from all locations..." -ForegroundColor Yellow

$configLocations = @(
    "C:\Program Files\OpenVPN\config",
    "C:\Program Files (x86)\OpenVPN\config", 
    "$env:USERPROFILE\OpenVPN\config",
    "$env:APPDATA\OpenVPN\config",
    "$env:LOCALAPPDATA\OpenVPN\config",
    "$env:PROGRAMDATA\OpenVPN\config",
    "$env:ALLUSERSPROFILE\OpenVPN\config"
)

$totalRemoved = 0

foreach ($location in $configLocations) {
    if (Test-Path $location) {
        Write-Host "📁 Checking: $location" -ForegroundColor Cyan
        
        $ovpnFiles = Get-ChildItem -Path $location -Filter "*.ovpn" -ErrorAction SilentlyContinue
        
        if ($ovpnFiles) {
            foreach ($file in $ovpnFiles) {
                try {
                    Remove-Item $file.FullName -Force
                    Write-Host "   ✅ Removed: $($file.Name)" -ForegroundColor Green
                    $totalRemoved++
                } catch {
                    Write-Host "   ❌ Failed to remove: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "   📄 No .ovpn files found" -ForegroundColor Gray
        }
    } else {
        Write-Host "   ❌ Directory not found: $location" -ForegroundColor Gray
    }
}

# Step 3: Clear OpenVPN GUI registry entries
Write-Host ""
Write-Host "🧹 Clearing OpenVPN GUI registry entries..." -ForegroundColor Yellow

$registryPaths = @(
    "HKCU:\Software\OpenVPN-GUI",
    "HKCU:\Software\OpenVPN-GUI\configs",
    "HKLM:\Software\OpenVPN-GUI",
    "HKLM:\Software\OpenVPN-GUI\configs"
)

foreach ($regPath in $registryPaths) {
    try {
        if (Test-Path $regPath) {
            Remove-Item -Path $regPath -Recurse -Force
            Write-Host "   ✅ Cleared: $regPath" -ForegroundColor Green
        } else {
            Write-Host "   📄 Not found: $regPath" -ForegroundColor Gray
        }
    } catch {
        Write-Host "   ⚠️ Could not clear: $regPath - $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Step 4: Clear OpenVPN GUI settings files
Write-Host ""
Write-Host "🗂️ Clearing OpenVPN GUI settings files..." -ForegroundColor Yellow

$settingsLocations = @(
    "$env:APPDATA\OpenVPN-GUI",
    "$env:LOCALAPPDATA\OpenVPN-GUI",
    "$env:USERPROFILE\.openvpn-gui"
)

foreach ($settingsPath in $settingsLocations) {
    if (Test-Path $settingsPath) {
        try {
            Remove-Item -Path $settingsPath -Recurse -Force
            Write-Host "   ✅ Cleared settings: $settingsPath" -ForegroundColor Green
        } catch {
            Write-Host "   ⚠️ Could not clear settings: $settingsPath" -ForegroundColor Yellow
        }
    }
}

# Step 5: Wait and restart OpenVPN GUI
Write-Host ""
Write-Host "🔄 Restarting OpenVPN GUI..." -ForegroundColor Yellow

Start-Sleep -Seconds 2

$openvpnPaths = @(
    "C:\Program Files\OpenVPN\bin\openvpn-gui.exe",
    "C:\Program Files (x86)\OpenVPN\bin\openvpn-gui.exe"
)

$started = $false
foreach ($path in $openvpnPaths) {
    if (Test-Path $path) {
        try {
            Start-Process -FilePath $path
            Write-Host "✅ OpenVPN GUI restarted from: $path" -ForegroundColor Green
            $started = $true
            break
        } catch {
            Write-Host "⚠️ Failed to start from: $path" -ForegroundColor Yellow
        }
    }
}

if (-not $started) {
    Write-Host "❌ Could not find or start OpenVPN GUI" -ForegroundColor Red
}

# Step 6: Final verification
Write-Host ""
Write-Host "🔍 Waiting for OpenVPN GUI to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🎉 REMOVAL COMPLETE!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "📊 Summary:" -ForegroundColor White
Write-Host "   • Removed $totalRemoved .ovpn files" -ForegroundColor White
Write-Host "   • Cleared registry entries" -ForegroundColor White  
Write-Host "   • Cleared settings files" -ForegroundColor White
Write-Host "   • Restarted OpenVPN GUI" -ForegroundColor White
Write-Host ""
Write-Host "💡 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Right-click OpenVPN tray icon to verify no servers" -ForegroundColor White
Write-Host "   2. If servers still appear, restart your computer" -ForegroundColor White
Write-Host "   3. Import new servers using copy_to_openvpn.bat" -ForegroundColor White
Write-Host ""

# Return result for Electron app
Write-Host "RESULT: Removed $totalRemoved servers successfully"
