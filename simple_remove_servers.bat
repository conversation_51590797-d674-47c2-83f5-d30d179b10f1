@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    SIMPLE SERVER REMOVAL TOOL
echo ========================================
echo.

set /p confirm="Remove ALL servers from OpenVPN GUI? (y/n): "
if /i not "%confirm%"=="y" (
    echo Cancelled.
    pause
    exit /b 0
)

echo.
echo 🛑 Stopping OpenVPN processes...
taskkill /F /IM openvpn-gui.exe >nul 2>&1
taskkill /F /IM openvpn.exe >nul 2>&1
echo ✅ Processes stopped

echo.
echo 🗑️ Removing config files...

set total_removed=0

REM Method 1: Program Files
echo [1] Checking Program Files location...
if exist "C:\Program Files\OpenVPN\config" (
    pushd "C:\Program Files\OpenVPN\config"
    for %%f in (*.ovpn) do (
        if exist "%%f" (
            del "%%f" >nul 2>&1
            if not exist "%%f" (
                echo    ✅ Removed: %%f
                set /a total_removed+=1
            ) else (
                echo    ❌ Could not remove: %%f
            )
        )
    )
    popd
) else (
    echo    ❌ Directory not found
)

REM Method 2: User Profile
echo [2] Checking User Profile location...
set "user_config=%USERPROFILE%\OpenVPN\config"
if exist "!user_config!" (
    pushd "!user_config!"
    for %%f in (*.ovpn) do (
        if exist "%%f" (
            del "%%f" >nul 2>&1
            if not exist "%%f" (
                echo    ✅ Removed: %%f
                set /a total_removed+=1
            ) else (
                echo    ❌ Could not remove: %%f
            )
        )
    )
    popd
) else (
    echo    ❌ Directory not found
)

REM Method 3: AppData
echo [3] Checking AppData location...
set "appdata_config=%APPDATA%\OpenVPN\config"
if exist "!appdata_config!" (
    pushd "!appdata_config!"
    for %%f in (*.ovpn) do (
        if exist "%%f" (
            del "%%f" >nul 2>&1
            if not exist "%%f" (
                echo    ✅ Removed: %%f
                set /a total_removed+=1
            ) else (
                echo    ❌ Could not remove: %%f
            )
        )
    )
    popd
) else (
    echo    ❌ Directory not found
)

REM Method 4: ProgramData
echo [4] Checking ProgramData location...
set "programdata_config=%PROGRAMDATA%\OpenVPN\config"
if exist "!programdata_config!" (
    pushd "!programdata_config!"
    for %%f in (*.ovpn) do (
        if exist "%%f" (
            del "%%f" >nul 2>&1
            if not exist "%%f" (
                echo    ✅ Removed: %%f
                set /a total_removed+=1
            ) else (
                echo    ❌ Could not remove: %%f
            )
        )
    )
    popd
) else (
    echo    ❌ Directory not found
)

echo.
echo 🧹 Clearing registry...
reg delete "HKEY_CURRENT_USER\Software\OpenVPN-GUI" /f >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Registry cleared
) else (
    echo ⚠️ Registry may not exist or need admin rights
)

echo.
echo 🔄 Restarting OpenVPN GUI...
timeout /t 2 /nobreak >nul
start "" "C:\Program Files\OpenVPN\bin\openvpn-gui.exe" >nul 2>&1
if %errorlevel%==0 (
    echo ✅ OpenVPN GUI restarted
) else (
    echo ⚠️ Could not restart OpenVPN GUI
)

echo.
echo ========================================
echo 🎉 REMOVAL COMPLETE!
echo ========================================
echo.
echo 📊 Total files removed: !total_removed!
echo.
echo 💡 Next steps:
echo 1. Right-click OpenVPN tray icon
echo 2. Check if servers are gone
echo 3. If servers still appear, run as Administrator
echo.
echo RESULT: Removed !total_removed! servers
pause
