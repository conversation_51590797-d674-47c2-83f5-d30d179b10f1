const { ipc<PERSON><PERSON><PERSON> } = require('electron');

let servers = [];

let isConnecting = false;

// Initialize the application
document.addEventListener('DOMContentLoaded', async () => {
    await loadServers();
    await updateCurrentIP();
    await updateConnectionStatus();
    await updateSchedulerStatus();
    await updateCleanupStatus();
    setupEventListeners();

    // Update IP and status every 10 seconds
    setInterval(async () => {
        await updateCurrentIP();
        await updateConnectionStatus();
        await updateSchedulerStatus();
    }, 10000);

    // Update cleanup status every 30 seconds
    setInterval(updateCleanupStatus, 30000);

    // Listen for server list changes
    ipcRenderer.on('servers-changed', async () => {
        console.log('Server list changed, refreshing...');
        await loadServers();
        showNotification('Server list updated!', 'info');
    });

    // Listen for popup handling events
    ipcRenderer.on('popup-handled', (event, message) => {
        console.log('Popup handled:', message);
        showNotification('🤖 Auto-clicked OK on popup', 'success');
    });

    // Listen for auto-rotation events
    ipcRenderer.on('auto-rotation-success', (event, data) => {
        if (data.isInitial) {
            showNotification(`🚀 Scheduler started! Connected to ${data.server}`, 'success');
        } else {
            showNotification(`🔄 Auto-rotated to ${data.server}! (${data.count})`, 'success');
        }
        updateSchedulerStatus();
        updateCurrentIP();
        updateConnectionStatus();
    });

    ipcRenderer.on('auto-rotation-failed', (event, data) => {
        if (data.isInitial) {
            showNotification(`❌ Scheduler failed to start: ${data.error}`, 'error');
        } else {
            showNotification(`❌ Auto-rotation failed: ${data.error}`, 'error');
        }
        updateSchedulerStatus();
    });
});

// Tab switching
function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected tab
    document.getElementById(`${tabName}-tab`).classList.add('active');
    event.target.classList.add('active');
}

// Update current IP
async function updateCurrentIP() {
    try {
        const result = await ipcRenderer.invoke('get-current-ip');
        const ipElement = document.getElementById('current-ip');

        if (result.error) {
            ipElement.textContent = 'Error';
            ipElement.className = 'value error';
        } else {
            ipElement.textContent = result.ip;
            ipElement.className = 'value';
        }
    } catch (error) {
        console.error('Error getting IP:', error);
        document.getElementById('current-ip').textContent = 'Error';
    }
}

// Update connection status
async function updateConnectionStatus() {
    try {
        const status = await ipcRenderer.invoke('get-connection-status');

        // Update status display
        const statusElements = {
            'rotation-status': document.getElementById('rotation-status'),
            'current-server': document.getElementById('current-server')
        };

        if (statusElements['rotation-status']) {
            statusElements['rotation-status'].textContent = status.isConnected ? 'Connected' : 'Disconnected';
        }

        if (statusElements['current-server']) {
            statusElements['current-server'].textContent = status.serverName || 'None';
        }

        // Update server list to show current connection
        updateServerListWithStatus(status);

    } catch (error) {
        console.error('Error getting connection status:', error);
    }
}

// Update server list to show which server is connected
function updateServerListWithStatus(status) {
    const serverItems = document.querySelectorAll('.server-item');
    serverItems.forEach(item => {
        const button = item.querySelector('button');
        const serverName = button.getAttribute('onclick').match(/'([^']+)'/)[1];

        if (status.isConnected && status.serverName === serverName) {
            item.classList.add('connected');
            button.textContent = '[CONNECTED]';
            button.disabled = true;
        } else {
            item.classList.remove('connected');
            button.textContent = '[CONNECT]';
            button.disabled = isConnecting;
        }
    });
}

// Load available VPN servers (your imported ones)
async function loadServers() {
    try {
        console.log('Loading servers...');

        // Test if ipcRenderer is available
        if (!ipcRenderer) {
            throw new Error('ipcRenderer not available');
        }

        servers = await ipcRenderer.invoke('get-vpn-servers');
        console.log('Received servers:', servers);

        if (!servers || !Array.isArray(servers)) {
            throw new Error('Invalid server data received');
        }

        // Update server count safely
        const serverCountEl = document.getElementById('server-count');
        if (serverCountEl) {
            serverCountEl.textContent = servers.length;
        }

        // Populate country filter
        const countries = [...new Set(servers.map(s => s.country))].sort();
        const countryFilter = document.getElementById('country-filter');
        if (countryFilter) {
            countryFilter.innerHTML = '<option value="">All Countries</option>';
            countries.forEach(country => {
                const option = document.createElement('option');
                option.value = country;
                option.textContent = country;
                countryFilter.appendChild(option);
            });
        }
        
        renderServers();
    } catch (error) {
        console.error('Error loading servers:', error);
        console.error('Error details:', error.message, error.stack);
        document.getElementById('servers-list').innerHTML = `<div class="error">Error loading servers: ${error.message}</div>`;

        // Fallback: show a basic message
        document.getElementById('server-count').textContent = '0';
    }
}

// Render servers list
function renderServers() {
    const container = document.getElementById('servers-list');

    if (!container) {
        console.error('servers-list container not found');
        return;
    }

    if (servers.length === 0) {
        container.innerHTML = '<div class="no-servers">No servers found</div>';
        return;
    }
    
    container.innerHTML = servers.map(server => `
        <div class="server-item" onclick="connectToServer('${server.name}')">
            <div class="server-info">
                <div class="server-name">${server.displayName}</div>
                <div class="server-details">Imported in OpenVPN GUI</div>
            </div>
            <button class="btn btn-small btn-primary" onclick="connectToServer('${server.name}')">
                [CONNECT]
            </button>
        </div>
    `).join('');
}

// Connect to a specific server with proper state management
async function connectToServer(serverName) {
    // Prevent multiple simultaneous connections
    if (isConnecting) {
        showNotification('Connection already in progress...', 'warning');
        return;
    }

    try {
        isConnecting = true;
        console.log(`Attempting to connect to ${serverName}`);

        // Show immediate feedback
        showNotification(`Connecting to ${serverName}...`, 'info');

        // Disable all connect buttons
        const connectButtons = document.querySelectorAll('.server-item button');
        connectButtons.forEach(btn => {
            btn.disabled = true;
            btn.textContent = '[CONNECTING...]';
        });

        // Get current connection status first
        const currentStatus = await ipcRenderer.invoke('get-connection-status');
        console.log('Current status:', currentStatus);

        if (currentStatus.isConnected && currentStatus.serverName === serverName) {
            showNotification(`Already connected to ${serverName}`, 'info');
            return;
        }

        // Attempt connection
        const result = await ipcRenderer.invoke('connect-to-vpn', serverName);
        console.log('Connection result:', result);

        if (result.success) {
            if (result.alreadyConnected) {
                showNotification(`Already connected to ${serverName}`, 'info');
            } else {
                showNotification(`✓ Connected to ${serverName}!`, 'success');
                if (result.newIP) {
                    showNotification(`New IP: ${result.newIP}`, 'success');
                }
            }

            // Update status immediately
            await updateConnectionStatus();
            await updateCurrentIP();

        } else {
            showNotification(`✗ Failed to connect: ${result.error}`, 'error');
            console.error('Connection failed:', result.error);
        }

    } catch (error) {
        console.error('Error connecting to server:', error);
        showNotification('Connection failed: ' + error.message, 'error');
    } finally {
        isConnecting = false;

        // Re-enable buttons and update status
        setTimeout(async () => {
            await updateConnectionStatus();

            const connectButtons = document.querySelectorAll('.server-item button');
            connectButtons.forEach(btn => {
                if (!btn.textContent.includes('[CONNECTED]')) {
                    btn.disabled = false;
                    btn.textContent = '[CONNECT]';
                }
            });
        }, 2000);
    }
}

// Connect to random server
async function connectToRandomServer() {
    if (servers.length === 0) {
        showNotification('No servers available', 'error');
        return;
    }
    
    const randomServer = servers[Math.floor(Math.random() * servers.length)];
    await connectToServer(randomServer.name);
}

// Disconnect VPN
async function disconnectVPN() {
    try {
        const result = await ipcRenderer.invoke('disconnect-vpn');
        showNotification(result.message, result.success ? 'success' : 'info');
        
        if (result.success) {
            setTimeout(updateCurrentIP, 5000);
        }
    } catch (error) {
        console.error('Error disconnecting:', error);
        showNotification('Disconnect failed', 'error');
    }
}

// Open OpenVPN GUI
async function openOpenVPNGUI() {
    try {
        const result = await ipcRenderer.invoke('open-openvpn-gui');
        showNotification(result.message, result.success ? 'success' : 'error');
    } catch (error) {
        console.error('Error opening OpenVPN GUI:', error);
        showNotification('Failed to open OpenVPN GUI', 'error');
    }
}

// Filter servers
function filterServers() {
    const searchTerm = document.getElementById('server-search').value.toLowerCase();
    const countryFilter = document.getElementById('country-filter').value;
    
    const filteredServers = servers.filter(server => {
        const matchesSearch = server.name.toLowerCase().includes(searchTerm) ||
                            server.displayName.toLowerCase().includes(searchTerm);
        const matchesCountry = !countryFilter || server.country === countryFilter;
        
        return matchesSearch && matchesCountry;
    });
    
    // Update the display
    const container = document.getElementById('servers-list');
    container.innerHTML = filteredServers.map(server => `
        <div class="server-item" onclick="connectToServer('${server.name}')">
            <div class="server-info">
                <div class="server-name">${server.displayName}</div>
                <div class="server-details">Imported in OpenVPN GUI</div>
            </div>
            <button class="btn btn-small btn-primary" onclick="connectToServer('${server.name}')">
                [CONNECT]
            </button>
        </div>
    `).join('');
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 4000);
}

// Refresh server list manually
async function refreshServerList() {
    try {
        showNotification('Refreshing server list...', 'info');
        await loadServers();
        showNotification('Server list refreshed!', 'success');
    } catch (error) {
        console.error('Error refreshing servers:', error);
        showNotification('Failed to refresh server list', 'error');
    }
}

// Scheduler functions
async function updateSchedulerStatus() {
    try {
        const status = await ipcRenderer.invoke('get-rotation-status');

        const elements = {
            'scheduler-status': document.getElementById('scheduler-status'),
            'rotation-count': document.getElementById('rotation-count'),
            'last-rotation': document.getElementById('last-rotation'),
            'next-rotation': document.getElementById('next-rotation')
        };

        if (elements['scheduler-status']) {
            elements['scheduler-status'].textContent = status.isRunning ? 'Running' : 'Stopped';
            elements['scheduler-status'].className = status.isRunning ? 'value success' : 'value';
        }

        if (elements['rotation-count']) {
            elements['rotation-count'].textContent = status.rotationCount || 0;
        }

        if (elements['last-rotation']) {
            elements['last-rotation'].textContent = status.lastRotation ?
                new Date(status.lastRotation).toLocaleTimeString() : 'Never';
        }

        if (elements['next-rotation']) {
            elements['next-rotation'].textContent = status.nextRotation ?
                new Date(status.nextRotation).toLocaleTimeString() : 'Not scheduled';
        }

        // Update button states
        const startBtn = document.getElementById('start-scheduler-btn');
        const stopBtn = document.getElementById('stop-scheduler-btn');

        if (startBtn && stopBtn) {
            startBtn.disabled = status.isRunning;
            stopBtn.disabled = !status.isRunning;

            if (status.isRunning) {
                startBtn.textContent = '[RUNNING...]';
                stopBtn.textContent = '[STOP SCHEDULER]';
            } else {
                startBtn.textContent = '[START SCHEDULER]';
                stopBtn.textContent = '[STOPPED]';
            }
        }

    } catch (error) {
        console.error('Error updating scheduler status:', error);
    }
}

async function startScheduler() {
    try {
        const intervalInput = document.getElementById('rotation-interval');
        const intervalMinutes = parseInt(intervalInput.value) || 30;

        showNotification(`🚀 Starting scheduler with immediate connection...`, 'info');

        // Disable start button immediately
        const startBtn = document.getElementById('start-scheduler-btn');
        if (startBtn) {
            startBtn.disabled = true;
            startBtn.textContent = '[CONNECTING...]';
        }

        const result = await ipcRenderer.invoke('start-auto-rotation', intervalMinutes);

        if (result.success) {
            showNotification(`✅ Scheduler started! (${intervalMinutes} min intervals)`, 'success');
            await updateSchedulerStatus();
        } else {
            showNotification(`❌ Scheduler failed: ${result.message}`, 'error');
            // Re-enable start button on failure
            if (startBtn) {
                startBtn.disabled = false;
                startBtn.textContent = '[START SCHEDULER]';
            }
        }
    } catch (error) {
        console.error('Error starting scheduler:', error);
        showNotification('Failed to start scheduler', 'error');

        // Re-enable start button on error
        const startBtn = document.getElementById('start-scheduler-btn');
        if (startBtn) {
            startBtn.disabled = false;
            startBtn.textContent = '[START SCHEDULER]';
        }
    }
}

async function stopScheduler() {
    try {
        showNotification('Stopping scheduler...', 'info');

        const result = await ipcRenderer.invoke('stop-auto-rotation');

        if (result.success) {
            showNotification('🛑 Scheduler stopped!', 'success');
            await updateSchedulerStatus();
        } else {
            showNotification(`Error: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('Error stopping scheduler:', error);
        showNotification('Failed to stop scheduler', 'error');
    }
}

function setInterval(minutes) {
    const intervalInput = document.getElementById('rotation-interval');
    if (intervalInput) {
        intervalInput.value = minutes;
    }
}

// Cleanup functions
async function updateCleanupStatus() {
    try {
        const status = await ipcRenderer.invoke('get-cleanup-status');

        const fileCountEl = document.getElementById('cleanup-file-count');
        const fileSizeEl = document.getElementById('cleanup-file-size');

        if (fileCountEl) {
            fileCountEl.textContent = status.unnecessaryFiles ?
                `${status.unnecessaryFiles.length} files` : 'Error';
        }

        if (fileSizeEl) {
            if (status.totalSize !== undefined) {
                const sizeKB = (status.totalSize / 1024).toFixed(1);
                fileSizeEl.textContent = `${sizeKB} KB`;
            } else {
                fileSizeEl.textContent = 'Error';
            }
        }

    } catch (error) {
        console.error('Error updating cleanup status:', error);
    }
}

async function cleanupNow() {
    try {
        showNotification('Cleaning up files...', 'info');

        const result = await ipcRenderer.invoke('cleanup-files');

        if (result.success) {
            showNotification(`🧹 Cleaned up ${result.cleanedCount} files!`, 'success');
            await updateCleanupStatus();
        } else {
            showNotification(`Cleanup error: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error during cleanup:', error);
        showNotification('Cleanup failed', 'error');
    }
}

// Server removal functions
async function removeAllServers() {
    try {
        // Show confirmation dialog
        const confirmed = confirm(
            '⚠️ WARNING: This will remove ALL servers from OpenVPN GUI!\n\n' +
            'This action cannot be undone.\n\n' +
            'Are you sure you want to continue?'
        );

        if (!confirmed) {
            return;
        }

        showNotification('🗑️ Removing all servers from OpenVPN GUI...', 'info');

        const result = await ipcRenderer.invoke('remove-all-servers');

        if (result.success) {
            showNotification(`✅ Removed ${result.removedCount} servers from OpenVPN GUI!`, 'success');

            // Refresh server list to reflect changes
            setTimeout(async () => {
                await loadServers();
                showNotification('Server list refreshed', 'info');
            }, 2000);
        } else {
            showNotification(`❌ Failed to remove servers: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error removing servers:', error);
        showNotification('Failed to remove servers', 'error');
    }
}

async function clearOpenVPNConfigs() {
    try {
        // Show confirmation dialog
        const confirmed = confirm(
            '⚠️ DANGER: This will DELETE ALL files from OpenVPN config directory!\n\n' +
            'This includes:\n' +
            '• All .ovpn configuration files\n' +
            '• All certificates and keys\n' +
            '• All other config files\n\n' +
            'This action CANNOT be undone!\n\n' +
            'Are you absolutely sure?'
        );

        if (!confirmed) {
            return;
        }

        // Double confirmation for this dangerous action
        const doubleConfirmed = confirm(
            '🚨 FINAL WARNING 🚨\n\n' +
            'You are about to permanently delete ALL files from the OpenVPN config directory.\n\n' +
            'Type "DELETE" in the next prompt to confirm.'
        );

        if (!doubleConfirmed) {
            return;
        }

        const deleteConfirmation = prompt('Type "DELETE" to confirm (case sensitive):');
        if (deleteConfirmation !== 'DELETE') {
            showNotification('❌ Action cancelled - confirmation text did not match', 'info');
            return;
        }

        showNotification('🗑️ Clearing OpenVPN config directory...', 'info');

        const result = await ipcRenderer.invoke('clear-openvpn-configs');

        if (result.success) {
            showNotification(`✅ Cleared ${result.clearedCount} files from OpenVPN config directory!`, 'success');

            // Refresh server list to reflect changes
            setTimeout(async () => {
                await loadServers();
                showNotification('Server list refreshed', 'info');
            }, 2000);
        } else {
            showNotification(`❌ Failed to clear configs: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error clearing configs:', error);
        showNotification('Failed to clear configs', 'error');
    }
}

// Setup event listeners
function setupEventListeners() {
    // Tools buttons
    document.getElementById('disconnect-btn').addEventListener('click', disconnectVPN);
    document.getElementById('refresh-ip-btn').addEventListener('click', updateCurrentIP);
    document.getElementById('random-server-btn').addEventListener('click', connectToRandomServer);
    document.getElementById('open-folder-btn').addEventListener('click', openOpenVPNGUI);

    // Add refresh button if it exists
    const refreshBtn = document.getElementById('refresh-servers-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshServerList);
    }

    // Scheduler buttons
    const startSchedulerBtn = document.getElementById('start-scheduler-btn');
    const stopSchedulerBtn = document.getElementById('stop-scheduler-btn');

    if (startSchedulerBtn) {
        startSchedulerBtn.addEventListener('click', startScheduler);
    }

    if (stopSchedulerBtn) {
        stopSchedulerBtn.addEventListener('click', stopScheduler);
    }

    // Cleanup buttons
    const cleanupNowBtn = document.getElementById('cleanup-now-btn');
    const refreshCleanupBtn = document.getElementById('refresh-cleanup-btn');

    if (cleanupNowBtn) {
        cleanupNowBtn.addEventListener('click', cleanupNow);
    }

    if (refreshCleanupBtn) {
        refreshCleanupBtn.addEventListener('click', updateCleanupStatus);
    }

    // Server removal buttons
    const removeAllServersBtn = document.getElementById('remove-all-servers-btn');
    const clearOpenVPNConfigsBtn = document.getElementById('clear-openvpn-configs-btn');

    if (removeAllServersBtn) {
        removeAllServersBtn.addEventListener('click', removeAllServers);
    }

    if (clearOpenVPNConfigsBtn) {
        clearOpenVPNConfigsBtn.addEventListener('click', clearOpenVPNConfigs);
    }

    // Server search and filter
    document.getElementById('server-search').addEventListener('input', filterServers);
    document.getElementById('country-filter').addEventListener('change', filterServers);
}

// Make functions available globally
window.showTab = showTab;
window.connectToServer = connectToServer;
