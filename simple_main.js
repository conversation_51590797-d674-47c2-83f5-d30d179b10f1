const { app, BrowserWindow, ipcMain, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const { exec, spawn } = require('child_process');

let mainWindow;
let currentConnection = {
  isConnected: false,
  serverName: null,
  connectedAt: null,
  lastKnownIP: null
};

let autoRotationScheduler = {
  isRunning: false,
  intervalMinutes: 30,
  intervalId: null,
  lastRotation: null,
  rotationCount: 0
};

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    icon: path.join(__dirname, 'icon.ico')
  });

  mainWindow.loadFile('index.html');
  
  // Remove menu bar
  mainWindow.setMenuBarVisibility(false);
}

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Helper functions for connection management
async function checkVPNStatus() {
  return new Promise((resolve) => {
    exec('tasklist /FI "IMAGENAME eq openvpn.exe"', (error, stdout, stderr) => {
      const isProcessRunning = stdout.includes('openvpn.exe');
      resolve(isProcessRunning);
    });
  });
}

async function getCurrentIP() {
  return new Promise((resolve) => {
    exec('powershell -Command "(Invoke-RestMethod -Uri \'https://api.ipify.org\' -TimeoutSec 5).Trim()"', (error, stdout, stderr) => {
      if (error) {
        resolve(null);
      } else {
        resolve(stdout.trim());
      }
    });
  });
}

async function waitForDisconnection(maxWaitSeconds = 15) {
  console.log('Waiting for complete disconnection...');

  for (let i = 0; i < maxWaitSeconds; i++) {
    const isStillConnected = await checkVPNStatus();
    if (!isStillConnected) {
      console.log(`✓ Disconnection confirmed after ${i + 1} seconds`);
      return true;
    }

    // Force kill any remaining processes every 3 seconds
    if (i % 3 === 0 && i > 0) {
      console.log(`Force killing remaining VPN processes (attempt ${Math.floor(i/3) + 1})...`);
      exec('taskkill /F /IM openvpn.exe', () => {});
      exec('taskkill /F /IM openvpn-gui.exe', () => {});
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Final force kill
  console.log('Final force kill of all VPN processes...');
  exec('taskkill /F /IM openvpn.exe', () => {});
  exec('taskkill /F /IM openvpn-gui.exe', () => {});

  // Wait a bit more after force kill
  await new Promise(resolve => setTimeout(resolve, 2000));

  const finalCheck = await checkVPNStatus();
  if (!finalCheck) {
    console.log('✓ Disconnection confirmed after force kill');
    return true;
  }

  console.log('⚠️ Warning: Some VPN processes may still be running');
  return false;
}

async function handleOpenVPNPopups() {
  // Use the simple PowerShell script to handle popups
  const scriptPath = path.join(__dirname, 'simple_popup_handler.ps1');

  exec(`powershell -ExecutionPolicy Bypass -File "${scriptPath}"`, (error, stdout, stderr) => {
    console.log('Popup handler output:', stdout);

    if (stdout.includes('SUCCESS: Popup handled') || stdout.includes('Sent ENTER')) {
      console.log('✓ Successfully handled OpenVPN popup');

      // Notify renderer about popup handling
      if (mainWindow) {
        mainWindow.webContents.send('popup-handled', 'Popup automatically closed');
      }
    } else if (stdout.includes('No OpenVPN popups found') || stdout.includes('INFO: No popups')) {
      console.log('🔍 No popups found to handle');
    }

    if (error) {
      console.log('Popup handler error:', error.message);
    }
  });
}

async function waitForConnection(serverName, maxWaitSeconds = 30) {
  console.log(`Waiting for connection to ${serverName}...`);
  const startIP = await getCurrentIP();

  // Start auto-handling popups every 2 seconds
  const popupHandler = setInterval(handleOpenVPNPopups, 2000);

  try {
    for (let i = 0; i < maxWaitSeconds; i++) {
      const isProcessRunning = await checkVPNStatus();
      const currentIP = await getCurrentIP();

      if (isProcessRunning && currentIP && currentIP !== startIP) {
        console.log(`Connection confirmed! IP changed from ${startIP} to ${currentIP}`);
        return { success: true, newIP: currentIP };
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('Connection timeout - VPN may not have connected properly');
    return { success: false, newIP: await getCurrentIP() };
  } finally {
    // Stop auto-handling popups
    clearInterval(popupHandler);
  }
}

async function waitForConnectionWithoutPopups(serverName, maxWaitSeconds = 30) {
  console.log(`Waiting for connection to ${serverName} (popup handling already scheduled)...`);
  const startIP = await getCurrentIP();

  // Give OpenVPN GUI time to start the connection
  await new Promise(resolve => setTimeout(resolve, 3000));

  for (let i = 0; i < maxWaitSeconds; i++) {
    const isProcessRunning = await checkVPNStatus();
    const currentIP = await getCurrentIP();

    // More lenient success criteria - either process running OR IP changed
    if (isProcessRunning || (currentIP && currentIP !== startIP)) {
      console.log(`Connection likely successful! Process=${isProcessRunning}, IP changed from ${startIP} to ${currentIP}`);
      return { success: true, newIP: currentIP };
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Even if timeout, check one more time and be optimistic
  const finalIP = await getCurrentIP();
  const finalProcess = await checkVPNStatus();

  if (finalProcess || (finalIP && finalIP !== startIP)) {
    console.log(`Connection detected after timeout! Process=${finalProcess}, IP=${finalIP}`);
    return { success: true, newIP: finalIP };
  }

  console.log('Connection timeout - VPN may not have connected properly');
  return { success: false, newIP: finalIP };
}

// Helper functions for connection management
async function checkVPNStatus() {
  return new Promise((resolve) => {
    exec('tasklist /FI "IMAGENAME eq openvpn.exe"', (error, stdout, stderr) => {
      const isProcessRunning = stdout.includes('openvpn.exe');
      resolve(isProcessRunning);
    });
  });
}

async function getCurrentIP() {
  return new Promise((resolve) => {
    exec('powershell -Command "(Invoke-RestMethod -Uri \'https://api.ipify.org\' -TimeoutSec 5).Trim()"', (error, stdout, stderr) => {
      if (error) {
        resolve(null);
      } else {
        resolve(stdout.trim());
      }
    });
  });
}

async function waitForDisconnection(maxWaitSeconds = 10) {
  console.log('Waiting for complete disconnection...');

  for (let i = 0; i < maxWaitSeconds; i++) {
    const isStillConnected = await checkVPNStatus();
    if (!isStillConnected) {
      console.log(`Disconnection confirmed after ${i + 1} seconds`);
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('Warning: VPN processes may still be running after timeout');
  return false;
}

async function waitForConnection(serverName, maxWaitSeconds = 30) {
  console.log(`Waiting for connection to ${serverName}...`);
  const startIP = await getCurrentIP();

  for (let i = 0; i < maxWaitSeconds; i++) {
    const isProcessRunning = await checkVPNStatus();
    const currentIP = await getCurrentIP();

    if (isProcessRunning && currentIP && currentIP !== startIP) {
      console.log(`Connection confirmed! IP changed from ${startIP} to ${currentIP}`);
      return { success: true, newIP: currentIP };
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('Connection timeout - VPN may not have connected properly');
  return { success: false, newIP: await getCurrentIP() };
}

// Get available VPN servers by scanning the ready_to_import directory
ipcMain.handle('get-vpn-servers', async () => {
  try {
    console.log('Getting VPN servers...');
    const servers = [];

    // Check ready_to_import directory for .ovpn files
    const importDir = path.join(__dirname, 'ready_to_import');
    console.log('Checking import directory:', importDir);
    if (fs.existsSync(importDir)) {
      const files = fs.readdirSync(importDir)
        .filter(file => file.endsWith('.ovpn'))
        .map(file => {
          const name = file.replace('.ovpn', '');
          const parts = name.split('-');
          const countryCode = parts[0]?.toUpperCase() || 'XX';
          const serverNum = parts[1] || '01';

          // Map country codes to names
          const countryNames = {
            'AF': 'Afghanistan',
            'AL': 'Albania',
            'AM': 'Armenia',
            'US': 'United States',
            'UK': 'United Kingdom',
            'DE': 'Germany',
            'FR': 'France',
            'NL': 'Netherlands',
            'CH': 'Switzerland',
            'SE': 'Sweden',
            'NO': 'Norway',
            'DK': 'Denmark',
            'FI': 'Finland',
            'IS': 'Iceland',
            'CA': 'Canada',
            'AU': 'Australia',
            'JP': 'Japan',
            'SG': 'Singapore',
            'HK': 'Hong Kong'
          };

          const countryName = countryNames[countryCode] || countryCode;

          return {
            name: name,
            country: countryCode,
            displayName: `${countryName}-${serverNum}`,
            filename: file
          };
        });

      servers.push(...files);
    }

    // Also check original openvpn_configs directory
    const configsDir = path.join(__dirname, 'openvpn_configs');
    if (fs.existsSync(configsDir)) {
      const files = fs.readdirSync(configsDir)
        .filter(file => file.endsWith('.ovpn'))
        .map(file => {
          const name = file.replace('.ovpn', '');
          const parts = name.split('-');
          const countryCode = parts[0]?.toUpperCase() || 'XX';
          const serverNum = parts[1] || '01';

          const countryNames = {
            'AF': 'Afghanistan',
            'AL': 'Albania',
            'AM': 'Armenia'
          };

          const countryName = countryNames[countryCode] || countryCode;

          return {
            name: name,
            country: countryCode,
            displayName: `${countryName}-${serverNum} (Original)`,
            filename: file,
            isOriginal: true
          };
        });

      // Add original files that aren't already in ready_to_import
      files.forEach(file => {
        if (!servers.find(s => s.name === file.name)) {
          servers.push(file);
        }
      });
    }

    console.log(`Found ${servers.length} VPN servers`);
    return servers;

  } catch (error) {
    console.error('Error getting VPN servers:', error);
    return [];
  }
});

// Connect to VPN with proper state management
ipcMain.handle('connect-to-vpn', async (event, serverName) => {
  try {
    console.log(`\n=== CONNECTING TO ${serverName} ===`);

    // Step 1: Check current connection status
    const isCurrentlyConnected = await checkVPNStatus();
    const currentIP = await getCurrentIP();

    console.log(`Current status: Connected=${isCurrentlyConnected}, IP=${currentIP}`);

    if (currentConnection.isConnected && currentConnection.serverName === serverName) {
      console.log(`Already connected to ${serverName}`);
      return { success: true, message: `Already connected to ${serverName}`, alreadyConnected: true };
    }

    // Step 2: Disconnect if currently connected
    if (isCurrentlyConnected || currentConnection.isConnected) {
      console.log(`Disconnecting from current server: ${currentConnection.serverName || 'Unknown'}`);

      // Trigger popup handler immediately when this message appears
      console.log('🎯 Triggering popup handler due to disconnect message...');
      setTimeout(handleOpenVPNPopups, 500);   // First attempt after 0.5s
      setTimeout(handleOpenVPNPopups, 2000);  // Second attempt after 2s
      setTimeout(handleOpenVPNPopups, 4000);  // Third attempt after 4s

      // Force disconnect using multiple methods
      const disconnectCommands = [
        `"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe" --disconnect_all`,
        `"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe" --command disconnect_all`,
        `taskkill /F /IM openvpn.exe`
      ];

      for (const cmd of disconnectCommands) {
        await new Promise((resolve) => {
          exec(cmd, (error, stdout, stderr) => {
            console.log(`Disconnect command: ${cmd}`);
            setTimeout(resolve, 1000);
          });
        });
      }

      // Wait and verify disconnection
      const disconnected = await waitForDisconnection(15);
      if (!disconnected) {
        console.log('Warning: Disconnection may not be complete, but proceeding...');
      }

      // Update connection state
      currentConnection.isConnected = false;
      currentConnection.serverName = null;
      currentConnection.connectedAt = null;
    }

    // Step 3: Check disconnection status (but don't fail if processes still running)
    const finalCheck = await checkVPNStatus();
    if (finalCheck) {
      console.log('⚠️ Warning: Some VPN processes still running, but proceeding with connection...');
      // Don't return error - proceed anyway as new connection might work
    }

    console.log('✓ Disconnection confirmed. Proceeding with new connection...');

    // Step 4: Connect to new server
    console.log(`Connecting to ${serverName}...`);

    // First ensure OpenVPN GUI is running
    exec('"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe"', (error) => {
      if (error) {
        console.log('Starting OpenVPN GUI...');
      }
    });

    // Wait a moment for GUI to start
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Connect using the actual file path from ready_to_import
    const configFilePath = path.join(__dirname, 'ready_to_import', `${serverName}.ovpn`);
    const connectCommand = `"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe" --config "${configFilePath}"`;

    exec(connectCommand, (error, stdout, stderr) => {
      console.log(`Connection command executed for ${serverName} using file: ${configFilePath}`);
      if (error) {
        console.log(`Connection command error: ${error.message}`);

        // Try alternative method with import
        console.log('Trying alternative method with import...');
        const importCommand = `"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe" --import "${configFilePath}"`;
        exec(importCommand, (error2) => {
          if (error2) {
            console.log(`Import failed: ${error2.message}`);
          } else {
            console.log('Import succeeded, now trying to connect...');
            // After import, try to connect by name
            setTimeout(() => {
              exec(`"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe" --connect "${serverName}"`, (error3) => {
                if (error3) {
                  console.log(`Post-import connection failed: ${error3.message}`);
                } else {
                  console.log('Post-import connection succeeded');
                }
              });
            }, 2000);
          }
        });
      } else {
        console.log('Direct file connection succeeded');
      }
    });

    // Step 5: Wait and verify connection (popup already handled during disconnect)
    const connectionResult = await waitForConnectionWithoutPopups(serverName, 30);

    if (connectionResult.success) {
      // Update connection state
      currentConnection.isConnected = true;
      currentConnection.serverName = serverName;
      currentConnection.connectedAt = new Date();
      currentConnection.lastKnownIP = connectionResult.newIP;

      console.log(`✓ Successfully connected to ${serverName}`);
      console.log(`✓ New IP: ${connectionResult.newIP}`);

      return {
        success: true,
        message: `Connected to ${serverName}`,
        newIP: connectionResult.newIP,
        serverName: serverName
      };
    } else {
      console.log(`✗ Failed to connect to ${serverName}`);
      return {
        success: false,
        error: `Failed to connect to ${serverName}`,
        currentIP: connectionResult.newIP
      };
    }

  } catch (error) {
    console.error('Error in connect-to-vpn:', error);
    return { success: false, error: error.message };
  }
});

// Disconnect VPN
ipcMain.handle('disconnect-vpn', async () => {
  try {
    console.log('Disconnecting all VPN connections...');

    // Use multiple methods to ensure disconnection
    const disconnectCommands = [
      `"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe" --disconnect_all`,
      `"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe" --command disconnect_all`,
      `taskkill /F /IM openvpn.exe`
    ];

    for (const cmd of disconnectCommands) {
      await new Promise((resolve) => {
        exec(cmd, (error, stdout, stderr) => {
          console.log(`Disconnect command: ${cmd}`);
          if (stdout) console.log('Output:', stdout);
          setTimeout(resolve, 1000);
        });
      });
    }

    return { success: true, message: 'VPN disconnected' };
  } catch (error) {
    console.error('Error disconnecting VPN:', error);
    return { success: false, error: error.message };
  }
});

// Get current IP address
ipcMain.handle('get-current-ip', async () => {
  return new Promise((resolve) => {
    exec('powershell -Command "(Invoke-RestMethod -Uri \'https://api.ipify.org\' -TimeoutSec 10).Trim()"', (error, stdout, stderr) => {
      if (error) {
        resolve({ error: error.message });
      } else {
        resolve({ ip: stdout.trim() });
      }
    });
  });
});

// Open OpenVPN GUI
ipcMain.handle('open-openvpn-gui', async () => {
  try {
    exec('"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe"');
    return { success: true, message: 'OpenVPN GUI opened' };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Get detailed connection status
ipcMain.handle('get-connection-status', async () => {
  try {
    const isVPNRunning = await checkVPNStatus();
    const currentIP = await getCurrentIP();

    // Update connection state based on actual status
    if (!isVPNRunning && currentConnection.isConnected) {
      console.log('VPN process not running, updating connection state');
      currentConnection.isConnected = false;
      currentConnection.serverName = null;
    }

    return {
      isConnected: currentConnection.isConnected,
      serverName: currentConnection.serverName,
      connectedAt: currentConnection.connectedAt,
      currentIP: currentIP,
      lastKnownIP: currentConnection.lastKnownIP,
      vpnProcessRunning: isVPNRunning
    };
  } catch (error) {
    console.error('Error getting connection status:', error);
    return {
      isConnected: false,
      serverName: null,
      currentIP: null,
      error: error.message
    };
  }
});

// Check if OpenVPN is running (legacy)
ipcMain.handle('check-openvpn-status', async () => {
  const status = await ipcMain.handle('get-connection-status')();
  return { running: status.vpnProcessRunning };
});

// Refresh server list (for when new files are added)
ipcMain.handle('refresh-servers', async () => {
  // This will trigger a fresh scan of the directories
  return await ipcMain.handle('get-vpn-servers')();
});

// Auto-rotation IPC handlers
ipcMain.handle('start-auto-rotation', async (event, intervalMinutes) => {
  return await startAutoRotation(intervalMinutes);
});

ipcMain.handle('stop-auto-rotation', async () => {
  return await stopAutoRotation();
});

ipcMain.handle('get-rotation-status', async () => {
  return {
    isRunning: autoRotationScheduler.isRunning,
    intervalMinutes: autoRotationScheduler.intervalMinutes,
    lastRotation: autoRotationScheduler.lastRotation,
    rotationCount: autoRotationScheduler.rotationCount,
    nextRotation: autoRotationScheduler.isRunning ?
      new Date(autoRotationScheduler.lastRotation.getTime() + (autoRotationScheduler.intervalMinutes * 60 * 1000)) :
      null
  };
});

// File cleanup IPC handlers
ipcMain.handle('cleanup-files', async () => {
  return await cleanupUnnecessaryFiles();
});

ipcMain.handle('get-cleanup-status', async () => {
  try {
    const unnecessaryFiles = [
      'auth.txt', 'test1.log', 'test2.log', 'test3.log', 'openvpn.log'
    ];

    const existingFiles = unnecessaryFiles.filter(file =>
      fs.existsSync(path.join(__dirname, file))
    );

    return {
      unnecessaryFiles: existingFiles,
      totalSize: existingFiles.reduce((size, file) => {
        try {
          const stats = fs.statSync(path.join(__dirname, file));
          return size + stats.size;
        } catch {
          return size;
        }
      }, 0)
    };
  } catch (error) {
    return { error: error.message };
  }
});

// Watch for file changes in VPN directories
function setupFileWatcher() {
  const watchDirs = [
    path.join(__dirname, 'ready_to_import'),
    path.join(__dirname, 'openvpn_configs')
  ];

  watchDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.watch(dir, (eventType, filename) => {
        if (filename && filename.endsWith('.ovpn')) {
          console.log(`VPN file changed: ${filename}`);
          // Notify renderer to refresh server list
          if (mainWindow) {
            mainWindow.webContents.send('servers-changed');
          }
        }
      });
    }
  });
}

// Auto-rotation functions
async function startAutoRotation(intervalMinutes = 30) {
  if (autoRotationScheduler.isRunning) {
    console.log('Auto-rotation already running');
    return { success: false, message: 'Auto-rotation already running' };
  }

  autoRotationScheduler.intervalMinutes = intervalMinutes;
  autoRotationScheduler.isRunning = true;
  autoRotationScheduler.rotationCount = 0;

  console.log(`🔄 Starting auto-rotation every ${intervalMinutes} minutes`);

  // IMMEDIATE FIRST CONNECTION when scheduler starts
  try {
    console.log(`\n🚀 Initial auto-rotation connection (starting scheduler)`);

    // Get available servers
    const servers = await getAvailableServers();
    if (servers.length === 0) {
      console.log('No servers available for initial connection');
      return { success: false, message: 'No servers available' };
    }

    // Pick a random server for initial connection
    const randomServer = servers[Math.floor(Math.random() * servers.length)];
    console.log(`🎯 Initial connection to: ${randomServer.name}`);

    // Connect to the server using the full connect-to-vpn logic
    const connectResult = await performVPNConnection(randomServer.name);

    if (connectResult.success) {
      autoRotationScheduler.lastRotation = new Date();
      autoRotationScheduler.rotationCount = 1;
      console.log(`✅ Initial connection successful! Server: ${randomServer.name}`);

      // Notify renderer about initial connection
      if (mainWindow) {
        mainWindow.webContents.send('auto-rotation-success', {
          server: randomServer.name,
          newIP: connectResult.newIP,
          count: autoRotationScheduler.rotationCount,
          isInitial: true
        });
      }

      // Clean up files after successful connection
      await cleanupUnnecessaryFiles();

    } else {
      console.log(`❌ Initial connection failed: ${connectResult.error}`);
      autoRotationScheduler.isRunning = false;

      if (mainWindow) {
        mainWindow.webContents.send('auto-rotation-failed', {
          server: randomServer.name,
          error: connectResult.error,
          isInitial: true
        });
      }

      return { success: false, message: `Initial connection failed: ${connectResult.error}` };
    }

  } catch (error) {
    console.error('Error in initial auto-rotation connection:', error);
    autoRotationScheduler.isRunning = false;
    return { success: false, message: `Initial connection error: ${error.message}` };
  }

  // NOW SET UP THE RECURRING ROTATION TIMER
  autoRotationScheduler.intervalId = setInterval(async () => {
    try {
      console.log(`\n⏰ Scheduled auto-rotation triggered (${autoRotationScheduler.rotationCount + 1})`);

      // Get available servers
      const servers = await getAvailableServers();
      if (servers.length === 0) {
        console.log('No servers available for rotation');
        return;
      }

      // Pick a random server different from current
      let randomServer;
      let attempts = 0;
      do {
        randomServer = servers[Math.floor(Math.random() * servers.length)];
        attempts++;
      } while (randomServer.name === currentConnection.serverName && attempts < 10);

      console.log(`🎯 Auto-rotating to: ${randomServer.name}`);

      // Connect to the new server
      const result = await performVPNConnection(randomServer.name);

      if (result.success) {
        autoRotationScheduler.lastRotation = new Date();
        autoRotationScheduler.rotationCount++;
        console.log(`✅ Auto-rotation successful! Count: ${autoRotationScheduler.rotationCount}`);

        // Notify renderer
        if (mainWindow) {
          mainWindow.webContents.send('auto-rotation-success', {
            server: randomServer.name,
            newIP: result.newIP,
            count: autoRotationScheduler.rotationCount
          });
        }

        // Clean up files after successful rotation
        await cleanupUnnecessaryFiles();

      } else {
        console.log(`❌ Auto-rotation failed: ${result.error}`);

        // Notify renderer about failure
        if (mainWindow) {
          mainWindow.webContents.send('auto-rotation-failed', {
            server: randomServer.name,
            error: result.error
          });
        }
      }

    } catch (error) {
      console.error('Error in auto-rotation:', error);
    }
  }, intervalMinutes * 60 * 1000);

  return { success: true, message: `Auto-rotation started with immediate connection (${intervalMinutes} min intervals)` };
}

async function stopAutoRotation() {
  if (!autoRotationScheduler.isRunning) {
    return { success: false, message: 'Auto-rotation not running' };
  }

  clearInterval(autoRotationScheduler.intervalId);
  autoRotationScheduler.isRunning = false;
  autoRotationScheduler.intervalId = null;

  console.log('🛑 Auto-rotation stopped');

  return {
    success: true,
    message: `Auto-rotation stopped (completed ${autoRotationScheduler.rotationCount} rotations)`
  };
}

async function getAvailableServers() {
  // Reuse the existing get-vpn-servers logic
  try {
    const servers = [];

    const importDir = path.join(__dirname, 'ready_to_import');
    if (fs.existsSync(importDir)) {
      const files = fs.readdirSync(importDir)
        .filter(file => file.endsWith('.ovpn'))
        .map(file => {
          const name = file.replace('.ovpn', '');
          return { name: name, filename: file };
        });
      servers.push(...files);
    }

    return servers;
  } catch (error) {
    console.error('Error getting servers for rotation:', error);
    return [];
  }
}

async function performVPNConnection(serverName) {
  // Use the full connect-to-vpn logic from the IPC handler
  try {
    console.log(`\n=== CONNECTING TO ${serverName} (Auto-Rotation) ===`);

    // Step 1: Check current connection status
    const isCurrentlyConnected = await checkVPNStatus();
    const currentIP = await getCurrentIP();

    console.log(`Current status: Connected=${isCurrentlyConnected}, IP=${currentIP}`);

    if (currentConnection.isConnected && currentConnection.serverName === serverName) {
      console.log(`Already connected to ${serverName}`);
      return { success: true, message: `Already connected to ${serverName}`, alreadyConnected: true };
    }

    // Step 2: Disconnect if currently connected
    if (isCurrentlyConnected || currentConnection.isConnected) {
      console.log(`Disconnecting from current server: ${currentConnection.serverName || 'Unknown'}`);

      // Trigger popup handler immediately when this message appears
      console.log('🎯 Triggering popup handler due to disconnect message...');
      setTimeout(handleOpenVPNPopups, 500);   // First attempt after 0.5s
      setTimeout(handleOpenVPNPopups, 2000);  // Second attempt after 2s
      setTimeout(handleOpenVPNPopups, 4000);  // Third attempt after 4s

      // Force disconnect using multiple methods
      const disconnectCommands = [
        `"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe" --disconnect_all`,
        `"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe" --command disconnect_all`,
        `taskkill /F /IM openvpn.exe`
      ];

      for (const cmd of disconnectCommands) {
        await new Promise((resolve) => {
          exec(cmd, (error, stdout, stderr) => {
            console.log(`Disconnect command: ${cmd}`);
            setTimeout(resolve, 1000);
          });
        });
      }

      // Wait and verify disconnection
      const disconnected = await waitForDisconnection(15);
      if (!disconnected) {
        console.log('Warning: Disconnection may not be complete, but proceeding...');
      }

      // Update connection state
      currentConnection.isConnected = false;
      currentConnection.serverName = null;
      currentConnection.connectedAt = null;
    }

    // Step 3: Ensure we're disconnected before connecting
    const finalCheck = await checkVPNStatus();
    if (finalCheck) {
      console.log('ERROR: VPN processes still running after disconnect attempts');
      return { success: false, error: 'Failed to disconnect from current server' };
    }

    console.log('✓ Disconnection confirmed. Proceeding with new connection...');

    // Step 4: Connect to new server using file path
    console.log(`Connecting to ${serverName}...`);
    const configFilePath = path.join(__dirname, 'ready_to_import', `${serverName}.ovpn`);
    const connectCommand = `"C:\\Program Files\\OpenVPN\\bin\\openvpn-gui.exe" --config "${configFilePath}"`;

    exec(connectCommand, (error, stdout, stderr) => {
      console.log(`Connection command executed for ${serverName} using file: ${configFilePath}`);
      if (error) {
        console.log(`Auto-rotation connection error: ${error.message}`);
      }
    });

    // Step 5: Wait and verify connection (popup already handled during disconnect)
    const connectionResult = await waitForConnectionWithoutPopups(serverName, 30);

    if (connectionResult.success) {
      // Update connection state
      currentConnection.isConnected = true;
      currentConnection.serverName = serverName;
      currentConnection.connectedAt = new Date();
      currentConnection.lastKnownIP = connectionResult.newIP;

      console.log(`✓ Successfully connected to ${serverName}`);
      console.log(`✓ New IP: ${connectionResult.newIP}`);

      return {
        success: true,
        message: `Connected to ${serverName}`,
        newIP: connectionResult.newIP,
        serverName: serverName
      };
    } else {
      console.log(`✗ Failed to connect to ${serverName}`);
      return {
        success: false,
        error: `Failed to connect to ${serverName}`,
        currentIP: connectionResult.newIP
      };
    }

  } catch (error) {
    console.error('Error in performVPNConnection:', error);
    return { success: false, error: error.message };
  }
}

// File cleanup functions
async function cleanupUnnecessaryFiles() {
  try {
    console.log('🧹 Starting file cleanup...');

    const filesToClean = [
      'auth.txt',
      'test1.log',
      'test2.log',
      'test3.log',
      'openvpn.log',
      'vpn_rotator.log.old',
      'temp_*.ovpn',
      'backup_*.json'
    ];

    let cleanedCount = 0;

    for (const filePattern of filesToClean) {
      if (filePattern.includes('*')) {
        // Handle wildcard patterns
        const files = fs.readdirSync(__dirname).filter(file => {
          const pattern = filePattern.replace('*', '.*');
          return new RegExp(pattern).test(file);
        });

        for (const file of files) {
          try {
            fs.unlinkSync(path.join(__dirname, file));
            console.log(`🗑️ Deleted: ${file}`);
            cleanedCount++;
          } catch (err) {
            // File might not exist or be in use
          }
        }
      } else {
        // Handle exact file names
        const filePath = path.join(__dirname, filePattern);
        if (fs.existsSync(filePath)) {
          try {
            fs.unlinkSync(filePath);
            console.log(`🗑️ Deleted: ${filePattern}`);
            cleanedCount++;
          } catch (err) {
            // File might be in use
          }
        }
      }
    }

    // Clean up old log files (keep only latest)
    await cleanupLogFiles();

    console.log(`✅ Cleanup complete! Removed ${cleanedCount} files`);

    return { success: true, cleanedCount };

  } catch (error) {
    console.error('Error during cleanup:', error);
    return { success: false, error: error.message };
  }
}

async function cleanupLogFiles() {
  try {
    const logFiles = fs.readdirSync(__dirname)
      .filter(file => file.endsWith('.log'))
      .map(file => ({
        name: file,
        path: path.join(__dirname, file),
        stats: fs.statSync(path.join(__dirname, file))
      }))
      .sort((a, b) => b.stats.mtime - a.stats.mtime); // Sort by modification time, newest first

    // Keep only the 3 most recent log files
    const filesToDelete = logFiles.slice(3);

    for (const file of filesToDelete) {
      try {
        fs.unlinkSync(file.path);
        console.log(`🗑️ Deleted old log: ${file.name}`);
      } catch (err) {
        // File might be in use
      }
    }

  } catch (error) {
    console.error('Error cleaning log files:', error);
  }
}

// Setup file watcher when app is ready
app.whenReady().then(() => {
  createWindow();
  setupFileWatcher();

  // Start cleanup on app start
  setTimeout(cleanupUnnecessaryFiles, 5000); // Clean up after 5 seconds
});
