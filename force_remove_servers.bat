@echo off
echo ========================================
echo    FORCE REMOVE ALL OPENVPN SERVERS
echo ========================================
echo.
echo This will forcefully remove ALL servers from OpenVPN GUI.
echo.
echo ⚠️  WARNING: This action cannot be undone!
echo.

set /p confirm="Are you sure you want to remove ALL servers? (y/n): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo 🚀 Starting force removal process...
echo.

REM Try to run PowerShell script with admin privileges
echo Running PowerShell script with elevated privileges...
powershell -Command "Start-Process PowerShell -ArgumentList '-ExecutionPolicy Bypass -File \"%~dp0force_remove_openvpn_servers.ps1\"' -Verb RunAs"

echo.
echo ⏳ Please check the PowerShell window that opened...
echo    It will show detailed progress of the removal process.
echo.
echo 💡 If no PowerShell window appeared, try running this batch file as Administrator.
echo.

REM Wait a moment then try alternative method
timeout /t 5 /nobreak >nul

echo 🔄 Running alternative removal method...
echo.

REM Alternative method without admin privileges
echo Stopping OpenVPN processes...
taskkill /F /IM openvpn-gui.exe >nul 2>&1
taskkill /F /IM openvpn.exe >nul 2>&1

echo Removing config files...
set removed_count=0

REM Remove from common locations
if exist "C:\Program Files\OpenVPN\config\*.ovpn" (
    echo Removing from Program Files...
    for %%f in ("C:\Program Files\OpenVPN\config\*.ovpn") do (
        del "%%f" >nul 2>&1
        if not exist "%%f" (
            echo   ✅ Removed: %%~nxf
            set /a removed_count+=1
        ) else (
            echo   ❌ Could not remove: %%~nxf
        )
    )
)

if exist "%USERPROFILE%\OpenVPN\config\*.ovpn" (
    echo Removing from User profile...
    for %%f in ("%USERPROFILE%\OpenVPN\config\*.ovpn") do (
        del "%%f" >nul 2>&1
        if not exist "%%f" (
            echo   ✅ Removed: %%~nxf
            set /a removed_count+=1
        ) else (
            echo   ❌ Could not remove: %%~nxf
        )
    )
)

if exist "%APPDATA%\OpenVPN\config\*.ovpn" (
    echo Removing from AppData...
    for %%f in ("%APPDATA%\OpenVPN\config\*.ovpn") do (
        del "%%f" >nul 2>&1
        if not exist "%%f" (
            echo   ✅ Removed: %%~nxf
            set /a removed_count+=1
        ) else (
            echo   ❌ Could not remove: %%~nxf
        )
    )
)

echo.
echo Clearing registry entries...
reg delete "HKEY_CURRENT_USER\Software\OpenVPN-GUI" /f >nul 2>&1
if %errorlevel%==0 (
    echo   ✅ Registry entries cleared
) else (
    echo   ⚠️ Registry entries may not exist or require admin privileges
)

echo.
echo Restarting OpenVPN GUI...
start "" "C:\Program Files\OpenVPN\bin\openvpn-gui.exe" >nul 2>&1
if %errorlevel%==0 (
    echo   ✅ OpenVPN GUI restarted
) else (
    echo   ⚠️ Could not restart OpenVPN GUI automatically
)

echo.
echo ========================================
echo 🎉 Force Removal Attempt Complete!
echo ========================================
echo.
echo 📊 Alternative method removed: %removed_count% files
echo.
echo 💡 Next steps:
echo    1. Right-click OpenVPN tray icon to check if servers are gone
echo    2. If servers still appear, restart your computer
echo    3. If problems persist, run this batch file as Administrator
echo    4. Import new servers using copy_to_openvpn.bat
echo.
echo 🔧 Troubleshooting:
echo    • Some files may require Administrator privileges to delete
echo    • Registry entries may need elevated permissions
echo    • A computer restart may be needed for complete cleanup
echo.
pause
