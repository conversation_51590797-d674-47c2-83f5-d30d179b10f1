/* Pixelated VPN Rotator Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: #000000;
    color: #ffffff;
    height: 100vh;
    overflow: hidden;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #111111;
    border: 3px solid #ffffff;
}

header {
    background: #000000;
    color: #ffffff;
    padding: 15px;
    border-bottom: 2px solid #ffffff;
}

header h1 {
    font-size: 18px;
    margin-bottom: 10px;
    text-align: center;
    font-weight: bold;
    letter-spacing: 2px;
}

.status-bar {
    display: flex;
    justify-content: space-around;
    background: #222222;
    border: 1px solid #ffffff;
    padding: 8px;
}

.status-item {
    text-align: center;
    border: 1px solid #333333;
    padding: 5px;
    background: #111111;
}

.status-item .label {
    display: block;
    font-size: 10px;
    margin-bottom: 3px;
    color: #888888;
}

.status-item .value {
    display: block;
    font-size: 12px;
    font-weight: bold;
    color: #ffffff;
}

.tabs {
    display: flex;
    background: #000000;
    border-bottom: 2px solid #ffffff;
}

.tab-button {
    flex: 1;
    padding: 12px;
    border: none;
    background: #222222;
    color: #ffffff;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    border-right: 1px solid #ffffff;
}

.tab-button:last-child {
    border-right: none;
}

.tab-button.active {
    background: #ffffff;
    color: #000000;
}

.tab-content {
    display: none;
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: #111111;
}

.tab-content.active {
    display: block;
}

.control-panel {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 15px;
    height: 100%;
}

.rotation-controls, .info-panel {
    background: #000000;
    border: 2px solid #ffffff;
    padding: 15px;
}

.rotation-controls h3, .info-panel h3 {
    margin-bottom: 15px;
    color: #ffffff;
    border-bottom: 1px solid #ffffff;
    padding-bottom: 8px;
    font-size: 14px;
}

.time-selector {
    margin-bottom: 15px;
}

.time-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #ffffff;
}

.time-input-group {
    display: flex;
    gap: 8px;
}

.time-input-group input {
    flex: 1;
    padding: 8px;
    border: 2px solid #ffffff;
    background: #000000;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.time-input-group select {
    padding: 8px;
    border: 2px solid #ffffff;
    background: #000000;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.button-group {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 15px;
    border: 2px solid #ffffff;
    background: #000000;
    color: #ffffff;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    flex: 1;
    min-width: 100px;
}

.btn:active {
    background: #ffffff;
    color: #000000;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #555555;
    color: #555555;
}

.btn-primary {
    background: #000000;
    color: #ffffff;
    border-color: #ffffff;
}

.btn-success {
    background: #000000;
    color: #ffffff;
    border-color: #ffffff;
}

.btn-danger {
    background: #000000;
    color: #ff0000;
    border-color: #ff0000;
}

.btn-secondary {
    background: #000000;
    color: #888888;
    border-color: #888888;
}

.btn-small {
    padding: 6px 10px;
    font-size: 10px;
    min-width: auto;
    flex: none;
}

.quick-times h4 {
    margin-bottom: 8px;
    color: #888888;
    font-size: 12px;
}

.quick-time-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.status-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.status-row {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    background: #222222;
    border: 1px solid #333333;
}

.servers-panel, .settings-panel, .logs-panel {
    background: #000000;
    border: 2px solid #ffffff;
    padding: 15px;
    height: 100%;
}

.server-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
}

.search-input, .filter-select {
    padding: 8px;
    border: 2px solid #ffffff;
    background: #000000;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    flex: 1;
}

.servers-list {
    height: calc(100vh - 300px);
    min-height: 500px;
    max-height: 700px;
    overflow-y: auto;
    border: 2px solid #ffffff;
    background: #111111;
}

.server-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #333333;
    background: #000000;
    transition: background-color 0.2s ease;
}

.server-item:hover {
    background: #111111;
    border-color: #555555;
}

.server-item:last-child {
    border-bottom: none;
}

.server-info {
    display: flex;
    flex-direction: column;
}

.server-name {
    font-weight: bold;
    color: #ffffff;
    font-size: 13px;
    margin-bottom: 2px;
}

.server-details {
    font-size: 11px;
    color: #aaaaaa;
    line-height: 1.3;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: bold;
    color: #ffffff;
    font-size: 12px;
}

.setting-group input, .setting-group select {
    width: 100%;
    padding: 8px;
    border: 2px solid #ffffff;
    background: #000000;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.file-input-group {
    display: flex;
    gap: 8px;
}

.file-input-group input {
    flex: 1;
}

.validation-results {
    margin-top: 15px;
    padding: 10px;
    border: 2px solid;
    display: none;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.validation-results.success {
    background: #111111;
    border-color: #ffffff;
    color: #ffffff;
}

.validation-results.error {
    background: #110000;
    border-color: #ff0000;
    color: #ff0000;
}

.validation-results.warning {
    background: #111100;
    border-color: #ffff00;
    color: #ffff00;
}

.log-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
}

.logs-container {
    height: 350px;
    overflow-y: auto;
    background: #000000;
    color: #ffffff;
    padding: 10px;
    border: 2px solid #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 3px;
    padding: 1px 0;
}

.log-entry.info {
    color: #00ffff;
}

.log-entry.warning {
    color: #ffff00;
}

.log-entry.error {
    color: #ff0000;
}

.log-entry.success {
    color: #ffffff;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #000000;
    border: 1px solid #333333;
}

::-webkit-scrollbar-thumb {
    background: #ffffff;
    border: 1px solid #000000;
}

::-webkit-scrollbar-thumb:hover {
    background: #cccccc;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border: 2px solid #ffffff;
    background: #000000;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    border-color: #00ff00;
    color: #00ff00;
}

.notification.error {
    border-color: #ff0000;
    color: #ff0000;
}

.notification.info {
    border-color: #00ffff;
    color: #00ffff;
}

.notification.warning {
    border-color: #ffff00;
    color: #ffff00;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Connected server styling */
.server-item.connected {
    border-color: #00ff00;
    background: #001100;
}

.server-item.connected .server-name {
    color: #00ff00;
}

.server-item.connected button {
    background: #00ff00;
    color: #000000;
    border-color: #00ff00;
}

/* Tool sections */
.tool-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ffffff;
}

.tool-section:last-child {
    border-bottom: none;
}

.tool-section h4 {
    margin-bottom: 10px;
    color: #ffffff;
    font-size: 12px;
}

.info-text {
    font-size: 11px;
    line-height: 1.4;
}

.info-text p {
    margin: 5px 0;
    color: #cccccc;
}

/* Scheduler styles */
.scheduler-panel {
    background: #000000;
    border: 2px solid #ffffff;
    padding: 15px;
    height: 100%;
}

.scheduler-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ffffff;
}

.scheduler-section:last-child {
    border-bottom: none;
}

.scheduler-section h4, .scheduler-section h5 {
    margin-bottom: 10px;
    color: #ffffff;
    font-size: 12px;
}

.status-display, .cleanup-info {
    background: #111111;
    border: 1px solid #ffffff;
    padding: 10px;
    margin-bottom: 10px;
}

.status-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 11px;
}

.status-row:last-child {
    margin-bottom: 0;
}

.status-row span:first-child {
    color: #cccccc;
}

.value.success {
    color: #00ff00;
}

.quick-intervals {
    margin: 10px 0;
}

.quick-intervals h5 {
    margin-bottom: 8px;
    font-size: 11px;
}

.btn-small {
    padding: 6px 10px;
    font-size: 10px;
    min-width: 50px;
}
