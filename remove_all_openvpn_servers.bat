@echo off
echo ========================================
echo    REMOVE ALL SERVERS FROM OPENVPN GUI
echo ========================================
echo.
echo This will completely remove all servers from OpenVPN GUI application.
echo.

REM Step 1: Stop OpenVPN GUI
echo 🛑 Stopping OpenVPN GUI...
taskkill /F /IM openvpn-gui.exe >nul 2>&1
taskkill /F /IM openvpn.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM Step 2: Remove config files from all possible locations
echo 🗑️ Removing config files...

set removed_count=0

REM Check Program Files location
if exist "C:\Program Files\OpenVPN\config\*.ovpn" (
    echo Removing from: C:\Program Files\OpenVPN\config\
    for %%f in ("C:\Program Files\OpenVPN\config\*.ovpn") do (
        del "%%f" >nul 2>&1
        if not exist "%%f" (
            echo   ✅ Removed: %%~nxf
            set /a removed_count+=1
        )
    )
)

REM Check Program Files (x86) location
if exist "C:\Program Files (x86)\OpenVPN\config\*.ovpn" (
    echo Removing from: C:\Program Files (x86)\OpenVPN\config\
    for %%f in ("C:\Program Files (x86)\OpenVPN\config\*.ovpn") do (
        del "%%f" >nul 2>&1
        if not exist "%%f" (
            echo   ✅ Removed: %%~nxf
            set /a removed_count+=1
        )
    )
)

REM Check User profile location
if exist "%USERPROFILE%\OpenVPN\config\*.ovpn" (
    echo Removing from: %USERPROFILE%\OpenVPN\config\
    for %%f in ("%USERPROFILE%\OpenVPN\config\*.ovpn") do (
        del "%%f" >nul 2>&1
        if not exist "%%f" (
            echo   ✅ Removed: %%~nxf
            set /a removed_count+=1
        )
    )
)

REM Check AppData location
if exist "%APPDATA%\OpenVPN\config\*.ovpn" (
    echo Removing from: %APPDATA%\OpenVPN\config\
    for %%f in ("%APPDATA%\OpenVPN\config\*.ovpn") do (
        del "%%f" >nul 2>&1
        if not exist "%%f" (
            echo   ✅ Removed: %%~nxf
            set /a removed_count+=1
        )
    )
)

REM Check ProgramData location
if exist "%PROGRAMDATA%\OpenVPN\config\*.ovpn" (
    echo Removing from: %PROGRAMDATA%\OpenVPN\config\
    for %%f in ("%PROGRAMDATA%\OpenVPN\config\*.ovpn") do (
        del "%%f" >nul 2>&1
        if not exist "%%f" (
            echo   ✅ Removed: %%~nxf
            set /a removed_count+=1
        )
    )
)

echo.
echo 🧹 Clearing OpenVPN GUI registry entries...
reg delete "HKEY_CURRENT_USER\Software\OpenVPN-GUI" /f >nul 2>&1
reg delete "HKEY_CURRENT_USER\Software\OpenVPN-GUI\configs" /f >nul 2>&1
echo   ✅ Registry entries cleared

echo.
echo 🔄 Restarting OpenVPN GUI...
start "" "C:\Program Files\OpenVPN\bin\openvpn-gui.exe"
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo 🎉 Removal Complete!
echo ✅ Removed %removed_count% server configuration files
echo ✅ Cleared OpenVPN GUI registry entries  
echo ✅ Restarted OpenVPN GUI
echo.
echo 💡 OpenVPN GUI should now show no servers.
echo    Right-click the tray icon to verify.
echo.
echo 🚀 You can now import new servers using:
echo    copy_to_openvpn.bat
echo.
pause
