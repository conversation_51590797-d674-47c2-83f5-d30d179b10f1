@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    IMPORT SERVERS TO USER PROFILE
echo ========================================
echo.
echo This script will copy servers to your user profile
echo OpenVPN directory (no admin rights needed).
echo.

REM Check for ready_to_import folder
if not exist "ready_to_import" (
    echo ❌ ready_to_import folder not found!
    echo Please run prepare_servers.bat first.
    pause
    exit /b 1
)

REM Use user profile OpenVPN directory
set "openvpn_config_dir=%USERPROFILE%\OpenVPN\config"

echo 📁 Target directory: %openvpn_config_dir%
echo.

REM Create directory if it doesn't exist
if not exist "%openvpn_config_dir%" (
    echo 📁 Creating OpenVPN config directory...
    mkdir "%openvpn_config_dir%" 2>nul
    
    if not exist "%openvpn_config_dir%" (
        echo ❌ Could not create directory: %openvpn_config_dir%
        pause
        exit /b 1
    )
)

echo ✅ OpenVPN config directory ready
echo.

REM Count files
set count=0
for %%f in (ready_to_import\*.ovpn) do (
    set /a count+=1
)

echo 🔍 Found !count! servers to import
echo.
echo 🚀 Starting copy process...
echo.

set copied=0
set failed=0

REM Copy each file
for %%f in (ready_to_import\*.ovpn) do (
    echo 📝 Copying: %%~nxf
    
    copy "%%f" "%openvpn_config_dir%\" >nul 2>&1
    
    if exist "%openvpn_config_dir%\%%~nxf" (
        echo    ✅ Success
        set /a copied+=1
    ) else (
        echo    ❌ Failed
        set /a failed+=1
    )
)

echo.
echo ========================================
echo 🎉 Import Complete!
echo ✅ Successfully imported: !copied! servers
if !failed! gtr 0 echo ❌ Failed to import: !failed! servers
echo.
echo 📁 Servers are in: %openvpn_config_dir%
echo.

if !copied! gtr 0 (
    echo 💡 To use these servers:
    echo 1. Open OpenVPN GUI
    echo 2. Right-click the tray icon
    echo 3. Go to "Import" and select files from: %openvpn_config_dir%
    echo 4. Or configure OpenVPN to use this directory
    echo.
    
    echo 🎯 Alternative: Use the Electron app which reads from ready_to_import directly
    echo.
    
    set /p open="Open the config directory now? (y/n): "
    if /i "!open!"=="y" (
        start "" "%openvpn_config_dir%"
    )
)

echo.
pause
